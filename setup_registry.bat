@echo off
echo YouTube Music Discord Rich Presence - Registry Setup
echo =====================================================
echo.
echo This script will register the native messaging host for Chrome and Brave browsers.
echo You need to run this as Administrator.
echo.
pause

set "MANIFEST_PATH=%~dp0%%USERPROFILE%%\YTMusicDiscordRP\com.discord.rich_presence.json"

echo Setting up for Chrome...
reg add "HKEY_CURRENT_USER\Software\Google\Chrome\NativeMessagingHosts\com.discord.rich_presence" /ve /t REG_SZ /d "%MANIFEST_PATH%" /f

echo Setting up for Brave...
reg add "HKEY_CURRENT_USER\Software\BraveSoftware\Brave-Browser\NativeMessagingHosts\com.discord.rich_presence" /ve /t REG_SZ /d "%MANIFEST_PATH%" /f

echo.
echo Registry setup complete!
echo.
echo Extension ID is already configured: blhfapacjdecfmmejdhplpobpdelpnin
echo.
echo You can now test the extension:
echo 1. Make sure Discord is running
echo 2. Open YouTube Music and play a song
echo 3. Check your Discord profile for the rich presence
echo.
pause
