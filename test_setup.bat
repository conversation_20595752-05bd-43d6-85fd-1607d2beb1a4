@echo off
echo YouTube Music Discord Rich Presence - Setup Test
echo =================================================
echo.

echo 1. Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo    Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Node.js is installed
    node --version
)

echo.
echo 2. Checking if we're in the correct directory...
if exist "host.js" (
    echo ✅ host.js found
) else (
    echo ❌ host.js not found
    echo    Please run this script from the extension directory
    pause
    exit /b 1
)

echo.
echo 3. Checking dependencies...
if exist "node_modules" (
    echo ✅ node_modules folder exists
) else (
    echo ❌ Dependencies not installed
    echo    Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
)

echo.
echo 4. Checking registry entries...
reg query "HKEY_CURRENT_USER\Software\Google\Chrome\NativeMessagingHosts\com.discord.rich_presence" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Chrome registry entry not found
    echo    Please run setup_registry.bat as Administrator
) else (
    echo ✅ Chrome registry entry found
)

reg query "HKEY_CURRENT_USER\Software\BraveSoftware\Brave-Browser\NativeMessagingHosts\com.discord.rich_presence" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Brave registry entry not found (optional)
) else (
    echo ✅ Brave registry entry found
)

echo.
echo 5. Checking manifest file...
if exist "com.discord.rich_presence.json" (
    echo ✅ Native manifest file exists
) else (
    echo ❌ com.discord.rich_presence.json not found
    pause
    exit /b 1
)

echo.
echo 6. Testing host script...
echo    Starting host.js for 3 seconds...
timeout /t 3 /nobreak | node host.js
if %errorlevel% neq 0 (
    echo ❌ host.js failed to start
    echo    Check the error messages above
) else (
    echo ✅ host.js started successfully
)

echo.
echo 7. Checking Discord/Vencord...
tasklist /fi "imagename eq Discord.exe" 2>nul | find /i "Discord.exe" >nul
if %errorlevel% neq 0 (
    tasklist /fi "imagename eq Vencord.exe" 2>nul | find /i "Vencord.exe" >nul
    if %errorlevel% neq 0 (
        echo ⚠️  Neither Discord nor Vencord is running
        echo    Please start Discord or Vencord
    ) else (
        echo ✅ Vencord is running
    )
) else (
    echo ✅ Discord is running
)

echo.
echo 8. Setup Summary:
echo ==================
echo ✅ = Working correctly
echo ❌ = Needs attention
echo ⚠️  = Warning (may be optional)
echo.
echo Next steps:
echo 1. Make sure Discord/Vencord is running
echo 2. Load the extension in Chrome (Developer Mode)
echo 3. Open YouTube Music and play a song
echo 4. Check the extension popup for connection status
echo.
pause
