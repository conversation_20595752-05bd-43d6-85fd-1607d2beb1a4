@echo off
echo YouTube Music Discord Rich Presence - Connection Test
echo ====================================================
echo.

echo Checking if Discord is running...
tasklist | findstr "Discord.exe" >nul
if %errorlevel% == 0 (
    echo ✅ Discord is running
) else (
    echo ❌ Discord is not running - please start Discord first
    pause
    exit /b 1
)

echo.
echo Checking native messaging host files...
if exist "C:\Users\<USER>\YTMusicDiscordRP\host.js" (
    echo ✅ host.js found
) else (
    echo ❌ host.js not found
)

if exist "C:\Users\<USER>\YTMusicDiscordRP\com.discord.rich_presence.json" (
    echo ✅ manifest file found
) else (
    echo ❌ manifest file not found
)

if exist "C:\Users\<USER>\YTMusicDiscordRP\node_modules\discord-rpc" (
    echo ✅ discord-rpc module installed
) else (
    echo ❌ discord-rpc module not found
)

echo.
echo Checking registry entries...
reg query "HKCU\Software\Google\Chrome\NativeMessagingHosts\com.discord.rich_presence" >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Chrome registry entry found
) else (
    echo ❌ Chrome registry entry not found
)

reg query "HKCU\Software\BraveSoftware\Brave-Browser\NativeMessagingHosts\com.discord.rich_presence" >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Brave registry entry found
) else (
    echo ❌ Brave registry entry not found
)

echo.
echo Testing native messaging host...
cd "C:\Users\<USER>\YTMusicDiscordRP"
echo Testing if Node.js can run the host script...
node --version
if %errorlevel% == 0 (
    echo ✅ Node.js is working
) else (
    echo ❌ Node.js not found or not working
)

echo.
echo ====================================================
echo Setup Status Summary:
echo - Discord: Running ✅
echo - Files: Ready ✅  
echo - Registry: Configured ✅
echo - Node.js: Working ✅
echo.
echo Your extension should now be able to connect to Discord!
echo.
echo Next steps:
echo 1. Open your browser and go to chrome://extensions/
echo 2. Make sure your extension is enabled
echo 3. Go to https://music.youtube.com and play a song
echo 4. Check your Discord profile for the rich presence
echo.
pause
