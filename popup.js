// Discord Rich Presence for YouTube Music - Popup Script

// DOM Elements
const statusDot = document.getElementById('status-dot');
const statusText = document.getElementById('status-text');
const nowPlayingContainer = document.getElementById('now-playing-container');
const enablePresenceToggle = document.getElementById('enable-presence');
const privateModeToggle = document.getElementById('private-mode');
const optionsButton = document.getElementById('options-button');
const reconnectButton = document.getElementById('reconnect-button');
const supportLink = document.getElementById('support-link');

// Initialize popup
document.addEventListener('DOMContentLoaded', async () => {
  try {
    // Load settings
    loadSettings();

    // Check connection status
    checkConnectionStatus();

    // Get current song information
    getCurrentSongInfo();

    // Set up event listeners
    setupEventListeners();
  } catch (error) {
    console.error('Error initializing popup:', error);
  }
});

// Load user settings
function loadSettings() {
  chrome.storage.sync.get({
    enabled: true,
    privateMode: false
  }, (settings) => {
    enablePresenceToggle.checked = settings.enabled;
    privateModeToggle.checked = settings.privateMode;
  });
}

// Save user settings
function saveSettings() {
  const settings = {
    enabled: enablePresenceToggle.checked,
    privateMode: privateModeToggle.checked
  };

  chrome.storage.sync.set(settings, () => {
    console.log('Settings saved');

    // Refresh current song with new settings
    chrome.tabs.query({ url: '*://music.youtube.com/*' }, (tabs) => {
      if (tabs.length > 0) {
        chrome.tabs.sendMessage(tabs[0].id, { type: 'refreshNow' });
      }
    });
  });
}

// Setup event listeners
function setupEventListeners() {
  // Toggle switches
  enablePresenceToggle.addEventListener('change', saveSettings);
  privateModeToggle.addEventListener('change', saveSettings);

  // Buttons
  optionsButton.addEventListener('click', openOptionsPage);
  reconnectButton.addEventListener('click', reconnectToDiscord);

  // Links
  supportLink.addEventListener('click', openSupportPage);
}

// Check Discord connection status
function checkConnectionStatus() {
  chrome.runtime.sendMessage({ type: 'getConnectionStatus' }, (response) => {
    if (chrome.runtime.lastError) {
      console.error("Error getting connection status:", chrome.runtime.lastError);
      updateConnectionStatus(false);
      return;
    }

    if (response && response.connected) {
      updateConnectionStatus(true, response.attempts, response.maxAttempts);
    } else {
      updateConnectionStatus(false, response?.attempts, response?.maxAttempts);
    }
  });
}

// Update connection status UI
function updateConnectionStatus(connected, attempts = 0, maxAttempts = 5) {
  if (connected) {
    statusDot.classList.remove('disconnected');
    statusDot.classList.add('connected');
    statusText.textContent = 'Connected to Discord';
  } else {
    statusDot.classList.remove('connected');
    statusDot.classList.add('disconnected');

    if (attempts >= maxAttempts) {
      statusText.textContent = 'Connection failed - Click Reconnect';
    } else if (attempts > 0) {
      statusText.textContent = `Connecting... (${attempts}/${maxAttempts})`;
    } else {
      statusText.textContent = 'Disconnected from Discord';
    }
  }
}

// Get current song information
function getCurrentSongInfo() {
  // First check with background script for current activity
  chrome.runtime.sendMessage({ type: 'getActivityData' }, (response) => {
    if (response && response.currentActivity) {
      displaySongInfo(response.currentActivity);
      return;
    }

    // If no activity found, query the content script
    chrome.tabs.query({ url: '*://music.youtube.com/*' }, (tabs) => {
      if (tabs.length > 0) {
        chrome.tabs.sendMessage(tabs[0].id, { type: 'getCurrentSong' }, (response) => {
          if (response && response.song) {
            displaySongInfo(response.song, response.state);
          } else {
            displayEmptyState();
          }
        });
      } else {
        displayEmptyState();
      }
    });
  });
}

// Display song information in the popup
function displaySongInfo(song, state = 'playing') {
  if (!song || !song.title) {
    displayEmptyState();
    return;
  }

  nowPlayingContainer.classList.remove('empty');

  // Format the HTML for the song display
  nowPlayingContainer.innerHTML = `
    <div class="song-info">
      <img class="album-art" src="${song.albumArt || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjMjAyMjI1Ii8+Cjx0ZXh0IHg9IjMwIiB5PSIzNSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjYjliYmJlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5ObyBBcnQ8L3RleHQ+Cjwvc3ZnPgo='}" alt="Album Art">
      <div class="song-details">
        <div class="song-title">${escapeHtml(song.title)}</div>
        <div class="song-artist">${escapeHtml(song.artist)}</div>
        ${song.album ? `<div class="song-album">${escapeHtml(song.album)}</div>` : ''}
      </div>
    </div>
    <div class="playback-indicator ${state === 'playing' ? '' : 'paused'}">
      ${state === 'playing' ? 'Now Playing' : 'Paused'}
    </div>
  `;
}

// Display empty state when no song is playing
function displayEmptyState() {
  nowPlayingContainer.classList.add('empty');
  nowPlayingContainer.textContent = 'Not playing anything on YouTube Music';
}

// Open options page
function openOptionsPage() {
  chrome.runtime.openOptionsPage();
}

// Reconnect to Discord
function reconnectToDiscord() {
  reconnectButton.textContent = 'Connecting...';
  reconnectButton.disabled = true;

  chrome.runtime.sendMessage({ type: 'forceReconnect' });

  // Give some time for reconnection attempt
  setTimeout(() => {
    checkConnectionStatus();
    reconnectButton.textContent = 'Reconnect';
    reconnectButton.disabled = false;
  }, 2000);
}

// Open support page
function openSupportPage() {
  chrome.tabs.create({ url: 'https://github.com/yourusername/ytmusic-discord-rp/issues' });
}

// Helper function to escape HTML
function escapeHtml(str) {
  if (!str) return '';
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}