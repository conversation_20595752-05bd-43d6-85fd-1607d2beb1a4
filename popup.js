// Discord Rich Presence for YouTube Music - Popup Script

// DOM Elements
const statusDot = document.getElementById('status-dot');
const statusText = document.getElementById('status-text');
const nowPlayingContainer = document.getElementById('now-playing-container');
const enablePresenceToggle = document.getElementById('enable-presence');
const privateModeToggle = document.getElementById('private-mode');
const optionsButton = document.getElementById('options-button');
const reconnectButton = document.getElementById('reconnect-button');
const supportLink = document.getElementById('support-link');

// Initialize popup
document.addEventListener('DOMContentLoaded', async () => {
  try {
    // Load settings
    loadSettings();

    // Check connection status
    checkConnectionStatus();

    // Get current song information
    getCurrentSongInfo();

    // Set up event listeners
    setupEventListeners();
  } catch (error) {
    console.error('Error initializing popup:', error);
  }
});

// Load user settings
function loadSettings() {
  chrome.storage.sync.get({
    enabled: true,
    privateMode: false
  }, (settings) => {
    enablePresenceToggle.checked = settings.enabled;
    privateModeToggle.checked = settings.privateMode;
  });
}

// Save user settings
function saveSettings() {
  const settings = {
    enabled: enablePresenceToggle.checked,
    privateMode: privateModeToggle.checked
  };

  chrome.storage.sync.set(settings, () => {
    console.log('Settings saved');

    // Refresh current song with new settings
    chrome.tabs.query({ url: '*://music.youtube.com/*' }, (tabs) => {
      if (tabs.length > 0) {
        chrome.tabs.sendMessage(tabs[0].id, { type: 'refreshNow' });
      }
    });
  });
}

// Setup event listeners
function setupEventListeners() {
  // Toggle switches
  enablePresenceToggle.addEventListener('change', saveSettings);
  privateModeToggle.addEventListener('change', saveSettings);

  // Buttons
  optionsButton.addEventListener('click', openOptionsPage);
  reconnectButton.addEventListener('click', reconnectToDiscord);

  // Links
  supportLink.addEventListener('click', openSupportPage);
}

// Check Discord connection status
function checkConnectionStatus() {
  chrome.runtime.sendMessage({ type: 'getConnectionStatus' }, (response) => {
    if (chrome.runtime.lastError) {
      console.error("Error getting connection status:", chrome.runtime.lastError);
      updateConnectionStatus(false);
      return;
    }

    if (response && response.connected) {
      updateConnectionStatus(true, response.attempts, response.maxAttempts);
    } else {
      updateConnectionStatus(false, response?.attempts, response?.maxAttempts);
    }
  });
}

// Update connection status UI
function updateConnectionStatus(connected, attempts = 0, maxAttempts = 5) {
  if (connected) {
    statusDot.classList.remove('disconnected');
    statusDot.classList.add('connected');
    statusText.textContent = 'Connected to Discord';
  } else {
    statusDot.classList.remove('connected');
    statusDot.classList.add('disconnected');

    if (attempts >= maxAttempts) {
      statusText.textContent = 'Connection failed - Click Reconnect';
    } else if (attempts > 0) {
      statusText.textContent = `Connecting... (${attempts}/${maxAttempts})`;
    } else {
      statusText.textContent = 'Disconnected from Discord';
    }
  }
}

// Get current song information
function getCurrentSongInfo() {
  // First check with background script for current activity
  chrome.runtime.sendMessage({ type: 'getActivityData' }, (response) => {
    if (response && response.currentActivity) {
      displaySongInfo(response.currentActivity);
      return;
    }

    // If no activity found, query the content script
    chrome.tabs.query({ url: '*://music.youtube.com/*' }, (tabs) => {
      if (tabs.length > 0) {
        chrome.tabs.sendMessage(tabs[0].id, { type: 'getCurrentSong' }, (response) => {
          if (response && response.song) {
            displaySongInfo(response.song, response.state);
          } else {
            displayEmptyState();
          }
        });
      } else {
        displayEmptyState();
      }
    });
  });
}

// Display song information in the popup
function displaySongInfo(song, state = 'playing') {
  if (!song || !song.title) {
    displayEmptyState();
    return;
  }

  nowPlayingContainer.classList.remove('empty');

  // Create enhanced display with all the rich information
  const enhancedHTML = createEnhancedSongDisplay(song, state);
  nowPlayingContainer.innerHTML = enhancedHTML;
}

// Create enhanced song display with rich information and visual effects
function createEnhancedSongDisplay(song, state) {
  // Create progress bar if we have duration info
  const progressBar = createProgressBarHTML(song);

  // Create status indicators
  const statusIndicators = createStatusIndicators(song);

  // Create queue info
  const queueInfo = createQueueInfo(song);

  // Create enhanced metadata
  const metadata = createMetadata(song);

  return `
    <div class="enhanced-song-info">
      <div class="album-art-container">
        <img class="album-art ${state === 'playing' ? 'rotating' : ''}"
             src="${song.albumArt || getDefaultAlbumArt()}"
             alt="Album Art">
        <div class="playback-overlay">
          <div class="playback-indicator ${state === 'playing' ? 'playing' : 'paused'}">
            ${state === 'playing' ? '▶️' : '⏸️'}
          </div>
        </div>
      </div>

      <div class="song-details">
        <div class="song-title-container">
          <div class="song-title">${escapeHtml(song.title)}</div>
          ${statusIndicators}
        </div>

        <div class="song-artist">${escapeHtml(song.artist)}</div>

        ${song.album ? `<div class="song-album">${escapeHtml(song.album)}${song.year ? ` (${song.year})` : ''}</div>` : ''}

        ${song.playlistName ? `<div class="playlist-info">📋 ${escapeHtml(song.playlistName)}</div>` : ''}

        ${progressBar}

        ${queueInfo}

        ${metadata}
      </div>
    </div>

    <div class="enhanced-status">
      <div class="status-row">
        <span class="status-label">${state === 'playing' ? '🎵 Now Playing' : '⏸️ Paused'}</span>
        ${song.volume < 100 ? `<span class="volume-info">🔊 ${song.volume}%</span>` : ''}
      </div>

      ${song.nextSong ? `<div class="next-song">⏭️ Next: ${escapeHtml(song.nextSong.slice(0, 40))}</div>` : ''}
    </div>
  `;
}

// Create progress bar HTML
function createProgressBarHTML(song) {
  if (!song.duration || !song.position) return '';

  const percentage = Math.round((song.position / song.duration) * 100);
  const currentTime = formatTime(song.position);
  const totalTime = formatTime(song.duration);

  return `
    <div class="progress-container">
      <div class="progress-bar">
        <div class="progress-fill" style="width: ${percentage}%"></div>
      </div>
      <div class="time-info">
        <span class="current-time">${currentTime}</span>
        <span class="total-time">${totalTime}</span>
      </div>
    </div>
  `;
}

// Create status indicators
function createStatusIndicators(song) {
  const indicators = [];

  if (song.isLive) indicators.push('🔴');
  if (song.isLiked) indicators.push('❤️');
  if (song.shuffleMode) indicators.push('🔀');
  if (song.repeatMode === 'one') indicators.push('🔂');
  if (song.repeatMode === 'all') indicators.push('🔁');

  return indicators.length > 0 ? `<div class="status-indicators">${indicators.join(' ')}</div>` : '';
}

// Create queue information
function createQueueInfo(song) {
  if (!song.queuePosition || !song.queueTotal) return '';

  return `
    <div class="queue-info">
      📊 ${song.queuePosition} of ${song.queueTotal} in queue
    </div>
  `;
}

// Create metadata information
function createMetadata(song) {
  const metadata = [];

  if (song.viewCount) metadata.push(`👁️ ${song.viewCount}`);
  if (song.channelName) metadata.push(`👤 ${song.channelName}`);

  return metadata.length > 0 ? `<div class="metadata">${metadata.join(' • ')}</div>` : '';
}

// Format time in MM:SS format
function formatTime(seconds) {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}

// Get default album art
function getDefaultAlbumArt() {
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjMjAyMjI1Ii8+Cjx0ZXh0IHg9IjMwIiB5PSIzNSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjYjliYmJlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5ObyBBcnQ8L3RleHQ+Cjwvc3ZnPgo=';
}

// Display empty state when no song is playing
function displayEmptyState() {
  nowPlayingContainer.classList.add('empty');
  nowPlayingContainer.textContent = 'Not playing anything on YouTube Music';
}

// Open options page
function openOptionsPage() {
  chrome.runtime.openOptionsPage();
}

// Reconnect to Discord
function reconnectToDiscord() {
  reconnectButton.textContent = 'Connecting...';
  reconnectButton.disabled = true;

  chrome.runtime.sendMessage({ type: 'forceReconnect' });

  // Give some time for reconnection attempt
  setTimeout(() => {
    checkConnectionStatus();
    reconnectButton.textContent = 'Reconnect';
    reconnectButton.disabled = false;
  }, 2000);
}

// Open support page
function openSupportPage() {
  chrome.tabs.create({ url: 'https://github.com/yourusername/ytmusic-discord-rp/issues' });
}

// Helper function to escape HTML
function escapeHtml(str) {
  if (!str) return '';
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}