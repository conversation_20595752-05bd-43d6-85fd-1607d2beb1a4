@echo off
title YouTube Music Discord RPC - Background Startup
echo Starting YouTube Music Discord RPC Background Server...

:: Change to the script directory
cd /d "%~dp0"

:: Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

:: Check if dependencies are installed
if not exist "node_modules\ws" (
    echo Installing dependencies...
    npm install ws discord-rpc
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

:: Kill any existing background server
taskkill /f /im node.exe /fi "WINDOWTITLE eq YouTube Music Discord RPC Background" >nul 2>&1

:: Start the background server (hidden)
echo Starting background server...
start /min "" node background-server.js

:: Wait a moment for server to start
timeout /t 3 /nobreak >nul

:: Check if server is running
netstat -an | find ":8765" >nul
if errorlevel 1 (
    echo ERROR: Background server failed to start
    echo Check the log file: ytmusic-rpc-background.log
    pause
    exit /b 1
) else (
    echo SUCCESS: Background server is running on port 8765
    echo The server will run in the background until you restart your computer.
    echo.
    echo To stop the server manually, run: stop-background-server.bat
    echo To check server status, run: check-server-status.bat
    echo.
    echo You can now close this window.
)

timeout /t 5 /nobreak >nul
