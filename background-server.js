// Background WebSocket Server for YouTube Music Discord RPC
// This version runs silently in the background

const WebSocket = require('ws');
const DiscordRPC = require('discord-rpc');
const fs = require('fs');
const path = require('path');

// Configuration
const WS_PORT = 8765;
const DISCORD_CLIENT_ID = '1234567890123456789'; // Replace with your Discord app ID
const LOG_FILE = path.join(__dirname, 'ytmusic-rpc-background.log');

// Global variables
let discordClient = null;
let isDiscordConnected = false;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;

// Logging function
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  
  // Write to log file
  fs.appendFileSync(LOG_FILE, logMessage);
  
  // Also log to console if not running as service
  if (process.env.NODE_ENV !== 'service') {
    console.log(`[${timestamp}] ${message}`);
  }
}

// Initialize Discord RPC connection
async function connectToDiscord() {
  try {
    if (discordClient) {
      discordClient.destroy();
    }

    discordClient = new DiscordRPC.Client({ transport: 'ipc' });
    
    discordClient.on('ready', () => {
      log(`Connected to Discord as ${discordClient.user.username}`);
      isDiscordConnected = true;
      reconnectAttempts = 0;
    });

    discordClient.on('disconnected', () => {
      log('Disconnected from Discord');
      isDiscordConnected = false;
      
      // Attempt to reconnect
      if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
        reconnectAttempts++;
        log(`Attempting to reconnect to Discord (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);
        setTimeout(connectToDiscord, 5000);
      } else {
        log('Max reconnection attempts reached. Will retry when new activity is received.');
      }
    });

    await discordClient.login({ clientId: DISCORD_CLIENT_ID });
    
  } catch (error) {
    log(`Failed to connect to Discord: ${error.message}`);
    isDiscordConnected = false;
    
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      reconnectAttempts++;
      log(`Retrying Discord connection (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);
      setTimeout(connectToDiscord, 5000);
    }
  }
}

// Set Discord activity
async function setDiscordActivity(activity) {
  if (!isDiscordConnected || !discordClient) {
    log('Discord not connected, attempting to connect...');
    await connectToDiscord();
    return;
  }

  try {
    await discordClient.setActivity(activity);
    log(`Updated Discord activity: ${activity.details || 'Unknown'}`);
  } catch (error) {
    log(`Failed to set Discord activity: ${error.message}`);
    isDiscordConnected = false;
  }
}

// Clear Discord activity
async function clearDiscordActivity() {
  if (!isDiscordConnected || !discordClient) {
    return;
  }

  try {
    await discordClient.clearActivity();
    log('Cleared Discord activity');
  } catch (error) {
    log(`Failed to clear Discord activity: ${error.message}`);
  }
}

// Create WebSocket server
function createWebSocketServer() {
  const wss = new WebSocket.Server({ 
    port: WS_PORT,
    host: 'localhost'
  });

  log(`WebSocket server listening on localhost:${WS_PORT}`);

  wss.on('connection', (ws) => {
    log('Extension connected to WebSocket server');

    ws.on('message', async (data) => {
      try {
        const message = JSON.parse(data);
        
        switch (message.type) {
          case 'set_activity':
            await setDiscordActivity(message.activity);
            break;
            
          case 'clear_activity':
            await clearDiscordActivity();
            break;
            
          case 'ping':
            ws.send(JSON.stringify({ type: 'pong' }));
            break;
            
          default:
            log(`Unknown message type: ${message.type}`);
        }
      } catch (error) {
        log(`Error processing WebSocket message: ${error.message}`);
      }
    });

    ws.on('close', () => {
      log('Extension disconnected from WebSocket server');
    });

    ws.on('error', (error) => {
      log(`WebSocket error: ${error.message}`);
    });
  });

  wss.on('error', (error) => {
    log(`WebSocket server error: ${error.message}`);
  });

  return wss;
}

// Handle process termination
function setupGracefulShutdown() {
  const shutdown = () => {
    log('Shutting down background server...');
    
    if (discordClient) {
      discordClient.destroy();
    }
    
    process.exit(0);
  };

  process.on('SIGINT', shutdown);
  process.on('SIGTERM', shutdown);
  process.on('SIGHUP', shutdown);
}

// Main function
async function main() {
  log('YouTube Music Discord RPC Background Server starting...');
  
  // Setup graceful shutdown
  setupGracefulShutdown();
  
  // Create WebSocket server
  const wss = createWebSocketServer();
  
  // Connect to Discord
  await connectToDiscord();
  
  log('Background server is running. Check log file for updates.');
  
  // Keep the process alive
  setInterval(() => {
    // Health check - ensure Discord connection is still alive
    if (!isDiscordConnected && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      log('Health check: Discord disconnected, attempting reconnection...');
      connectToDiscord();
    }
  }, 30000); // Check every 30 seconds
}

// Start the server
main().catch((error) => {
  log(`Fatal error: ${error.message}`);
  process.exit(1);
});
