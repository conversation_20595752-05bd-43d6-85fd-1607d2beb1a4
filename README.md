# 🎵 YouTube Music Enhanced Discord Rich Presence

Modern WebSocket-based Discord Rich Presence for YouTube Music with Spotify-style features and enhanced visuals.

## ✨ Features

- 🖼️ **Professional Images** - High-quality YouTube Music branding
- ⏱️ **Progress Bar** - Real-time elapsed time like Spotify
- 🎵 **Modern Formatting** - Beautiful emojis and enhanced text
- 🔗 **Clickable Buttons** - Direct links to song and YouTube Music
- 🎯 **Play Indicator** - Small icon showing playback status
- 🚀 **WebSocket Connection** - Modern, reliable communication

## 🖼️ About Images/Thumbnails

**Important:** Discord Rich Presence has strict limitations on images:

- ❌ External URLs (like YouTube thumbnails) are **NOT supported**
- ✅ Only pre-uploaded Discord application assets work reliably
- ✅ We use high-quality YouTube Music branding for a professional look

To enable custom images, you need to upload assets to Discord's Developer Portal.

## 🚀 Quick Start

### 1. Install Dependencies

```bash
npm install discord-rpc ws
```

### 2. Start the Server

Double-click `start-enhanced-rpc.bat` or run:

```bash
node websocket-host.js
```

### 3. Load Extension

1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode"
3. Click "Load unpacked" and select this folder
4. Reload the extension if already loaded

### 4. Set Up Discord Assets (Optional - for custom images)

```bash
# Get instructions for uploading Discord assets
node setup-discord-assets.js
```

### 5. Test the Setup

```bash
# Test Discord RPC connection and assets
node test-rpc.js
```

### 6. Use the Extension

1. Open YouTube Music and play a song
2. Check Discord - you should see enhanced presence with progress bar!

## 📁 Files

- `websocket-host.js` - WebSocket server that connects to Discord
- `background.js` - Extension background script
- `content.js` - YouTube Music page content script
- `popup.js/html` - Extension popup interface
- `options.js/html` - Extension settings page
- `manifest.json` - Extension manifest
- `start-enhanced-rpc.bat` - Easy server launcher

## 🎯 Discord Display

```
🎵 Song Title
👤 Artist • 💿 Album
[Song Thumbnail]     [▶️]
⏱️ 1:23 / 4:56 progress bar
🎵 Listen on YouTube Music | 🎧 Open YouTube Music
```

## 🔧 Troubleshooting

- **Server won't start**: Make sure Node.js is installed and run `npm install`
- **Extension not connecting**: Reload the extension and restart the server
- **No Discord presence**: Make sure Discord/Vencord is running and RPC is enabled

## 🎨 Customization

Edit settings in the extension popup to customize what information is displayed.

---

**Enjoy your enhanced YouTube Music Discord presence!** 🎉
