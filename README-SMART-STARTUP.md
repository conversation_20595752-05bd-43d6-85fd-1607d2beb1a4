# 🎵 YouTube Music Discord RPC - Smart Auto-Startup

## ✨ What's New?

Your Discord Rich Presence now shows **MUCH MORE INFORMATION** and starts **automatically** when you open YouTube Music!

### 🎨 Enhanced Discord Rich Presence Features:
- **🔴 Live indicators** for live streams
- **❤️ Like status** when you've liked a song
- **🔀 Shuffle & 🔁 Repeat** mode indicators
- **📊 Progress bars** with current time/total time
- **📋 Playlist information** with queue position
- **👁️ View counts** and channel information
- **⏭️ Next song preview**
- **🔊 Volume level** display
- **🎵 Multiple action buttons** (Listen, View Artist, View Playlist)

### 🚀 Smart Auto-Startup:
- **Automatic detection** when you open YouTube Music
- **Smart notifications** to guide you through setup
- **No manual server starting** required after initial setup

## 🛠️ Setup Instructions

### **Option 1: Quick Setup (Recommended)**
1. **Run the setup script:**
   ```
   Double-click: setup-auto-startup.bat
   ```
2. **Choose option 3** (Both options) for best experience
3. **Done!** The server will now start automatically

### **Option 2: Manual Setup**
1. **Install dependencies** (first time only):
   ```bash
   npm install ws discord-rpc
   ```
2. **Start the server:**
   ```
   Double-click: auto-start-server.bat
   ```

## 🎯 How It Works

1. **Open YouTube Music** in your browser
2. **Extension detects** you're on YouTube Music
3. **Automatic notification** appears if server isn't running
4. **Click the notification** or run `auto-start-server.bat`
5. **Rich presence appears** in Discord with enhanced information!

## 🎨 What You'll See in Discord

### **Enhanced Details Line:**
```
🔴❤️🔀 Song Title (2023) • HD
```

### **Enhanced State Line:**
```
Artist Name (1.2M views) • Album Name (2023) • 📋 My Playlist [3/15] • ████████░░ 2:34/4:12 • ⏭️ Next: Next Song
```

### **Smart Buttons:**
- 🎵 **Listen on YouTube Music**
- 👤 **View Artist**
- 📋 **View Playlist**

## 🔧 Advanced Features

### **Status Indicators:**
- 🔴 = Live stream
- ❤️ = Liked song
- 🔀 = Shuffle mode on
- 🔂 = Repeat one
- 🔁 = Repeat all
- 🔊 = Volume level (if not 100%)

### **Progress Information:**
- Visual progress bar: `████████░░`
- Current time / Total time
- Percentage complete

### **Queue Information:**
- Current position in queue: `[3/15]`
- Playlist name if available
- Next song preview

## 🚨 Troubleshooting

### **If Discord doesn't show your activity:**
1. Make sure Discord is running
2. Check that `auto-start-server.bat` is running
3. Reload the extension in Chrome
4. Click "Reconnect" in the extension popup

### **If you get a notification to start the server:**
1. Click the notification
2. Or manually run `auto-start-server.bat`
3. The extension will automatically connect

### **To stop the server:**
- Close the `auto-start-server.bat` window
- Or close YouTube Music tab

## 🎉 Enjoy Your Enhanced Discord Rich Presence!

Your friends will now see detailed information about what you're listening to, including progress, queue position, and much more!
