// Test script for YouTube Music thumbnails in Discord Rich Presence
const DiscordRPC = require('discord-rpc');

const clientId = '1375799244400623726';
const rpc = new DiscordRPC.Client({ transport: 'ipc' });

// Sample YouTube Music thumbnails to test
const testThumbnails = [
  {
    name: 'High Quality Thumbnail',
    url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
    song: 'Never Gonna Give You Up',
    artist: '<PERSON>'
  },
  {
    name: 'Alternative Quality',
    url: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg',
    song: 'Never Gonna Give You Up (HQ)',
    artist: '<PERSON>'
  },
  {
    name: 'Different Song',
    url: 'https://i.ytimg.com/vi/9bZkp7q19f0/maxresdefault.jpg',
    song: 'Gangnam Style',
    artist: 'PSY'
  }
];

let currentTest = 0;

rpc.on('ready', () => {
  console.log('🖼️  YouTube Music Thumbnail Test');
  console.log('=================================');
  console.log('✅ Connected to Discord!');
  console.log('');
  console.log('This will test REAL YouTube thumbnails in Discord Rich Presence');
  console.log('');
  console.log('🔧 Make sure you have:');
  console.log('1. Started the thumbnail service: node thumbnail-service.js');
  console.log('2. Thumbnail service running on http://localhost:3000');
  console.log('');
  console.log('Press Enter to start testing...');

  process.stdin.once('data', () => {
    runNextTest();
  });
});

function runNextTest() {
  if (currentTest >= testThumbnails.length) {
    console.log('');
    console.log('🎯 All thumbnail tests completed!');
    console.log('');
    console.log('Results Summary:');
    console.log('- If you saw actual song images: ✅ Thumbnails working!');
    console.log('- If you saw generic images: ❌ Thumbnail service not working');
    console.log('- If you saw no images: ❌ Discord assets not uploaded');
    console.log('');
    console.log('🚀 If thumbnails worked, your extension will now show');
    console.log('   REAL song artwork from YouTube Music!');
    console.log('');
    console.log('Press Ctrl+C to exit.');
    return;
  }

  const test = testThumbnails[currentTest];
  console.log(`🧪 Test ${currentTest + 1}/${testThumbnails.length}: ${test.name}`);
  console.log(`🎵 Song: ${test.song} - ${test.artist}`);

  // Create proxy URL for the thumbnail
  const proxyUrl = `http://localhost:3000/proxy/${encodeURIComponent(test.url)}`;
  console.log(`🔗 Proxy URL: ${proxyUrl}`);

  const activity = {
    details: `🎵 ${test.song}`,
    state: `👤 ${test.artist}`,
    assets: {
      large_image: proxyUrl,
      large_text: `${test.song} - ${test.artist}`,
      small_image: 'ytmusic_logo',
      small_text: '▶️ Playing'
    },
    timestamps: {
      start: Date.now(),
      end: Date.now() + 180000 // 3 minutes
    },
    buttons: [
      {
        label: '🎵 Listen on YouTube Music',
        url: 'https://music.youtube.com'
      }
    ]
  };

  console.log('📤 Setting Discord activity...');

  rpc.setActivity(activity)
    .then(() => {
      console.log('✅ Activity set successfully!');
      console.log('');
      console.log('👀 Check Discord now - do you see the song thumbnail?');
      console.log('');
      console.log('Expected: Real album artwork from YouTube');
      console.log('If you see a generic image, the thumbnail service may not be running.');
      console.log('');
      console.log('Press Enter for next test...');

      process.stdin.once('data', () => {
        currentTest++;
        runNextTest();
      });
    })
    .catch(error => {
      console.error('❌ Error setting activity:', error.message);
      console.log('');
      console.log('This might mean:');
      console.log('- Discord is not running');
      console.log('- Thumbnail service is not accessible');
      console.log('- Network connectivity issues');
      console.log('');
      console.log('Press Enter to try next test...');

      process.stdin.once('data', () => {
        currentTest++;
        runNextTest();
      });
    });
}

rpc.on('disconnected', () => {
  console.log('❌ Disconnected from Discord');
});

// Connect to Discord
console.log('🔌 Connecting to Discord...');
rpc.login({ clientId })
  .catch(error => {
    console.error('❌ Failed to connect to Discord:', error);
    console.log('');
    console.log('Make sure:');
    console.log('1. Discord is running');
    console.log('2. Discord RPC is enabled');
    console.log('3. If using Vencord, RPC plugin is enabled');
    console.log('');
    process.exit(1);
  });

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down...');
  if (rpc) {
    rpc.destroy();
  }
  process.exit(0);
});
