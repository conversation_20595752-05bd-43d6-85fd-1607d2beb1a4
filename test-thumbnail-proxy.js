// Test the thumbnail proxy directly
const DiscordRPC = require('discord-rpc');

const clientId = '1375799244400623726';
const rpc = new DiscordRPC.Client({ transport: 'ipc' });

// Test the exact URL from your logs
const testThumbnailUrl = 'https://i.ytimg.com/vi/qjssI0OlaB0/sddefault.jpg?sqp=-oaymwEWCJADEOEBIAQqCghqEJQEGHgg6AJIWg&rs=AMzJL3mgmkWRxflY6r5c6lTmdv6yRs6q-Q';

rpc.on('ready', () => {
  console.log('✅ Connected to Discord!');
  console.log('');
  console.log('🧪 Testing thumbnail proxy with your exact URL...');
  console.log('Original URL:', testThumbnailUrl);
  
  // Create proxy URL
  const proxiedUrl = `https://images.weserv.nl/?url=${encodeURIComponent(testThumbnailUrl)}&w=512&h=512&fit=cover`;
  console.log('Proxy URL:', proxiedUrl);
  console.log('');
  
  // Test the activity with the proxied thumbnail
  const activity = {
    details: '🎵 Qui sait ? (feat. ElGrandeToto)',
    state: '👤 Niro',
    assets: {
      large_image: proxiedUrl,
      large_text: 'Album Art Test'
    },
    timestamps: {
      start: Date.now(),
      end: Date.now() + 180000
    },
    buttons: [
      {
        label: '🎵 Listen on YouTube Music',
        url: 'https://music.youtube.com'
      }
    ]
  };

  console.log('Setting Discord activity with thumbnail...');
  
  rpc.setActivity(activity)
    .then(() => {
      console.log('✅ Activity set successfully!');
      console.log('');
      console.log('👀 Check Discord now!');
      console.log('');
      console.log('Expected: Real album art for "Qui sait ?" by Niro');
      console.log('If you see the thumbnail: ✅ Proxy works!');
      console.log('If you see question mark: ❌ Proxy/Discord issue');
      console.log('');
      console.log('You can also test the proxy URL directly in your browser:');
      console.log(proxiedUrl);
      console.log('');
      console.log('Press Ctrl+C to exit.');
    })
    .catch(error => {
      console.error('❌ Failed to set activity:', error);
      console.log('');
      console.log('This could mean:');
      console.log('- Discord is not running');
      console.log('- Discord RPC is disabled');
      console.log('- The proxy URL is invalid');
    });
});

rpc.on('disconnected', () => {
  console.log('❌ Disconnected from Discord');
});

// Connect to Discord
console.log('🔌 Connecting to Discord...');
rpc.login({ clientId })
  .catch(error => {
    console.error('❌ Failed to connect to Discord:', error);
    console.log('Make sure Discord is running and try again.');
  });

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down...');
  if (rpc) {
    rpc.destroy();
  }
  process.exit(0);
});
