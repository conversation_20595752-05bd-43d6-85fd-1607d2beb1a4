

---

# Discord Rich Presence Extension for YouTube Music on Brave Browser

## 1. Introduction
This project aims to develop a browser extension for Brave that enables Discord Rich Presence integration with YouTube Music. The extension will display detailed information about a user's currently playing music in their Discord profile, enhancing social connectivity and music sharing among Discord users.

## 2. Problem Statement
Currently, there is no native way for YouTube Music users on the Brave browser to display their listening activity on Discord through Rich Presence. Unlike dedicated music applications such as Spotify which have built-in Discord integration, web-based music streaming on YouTube Music lacks this functionality, creating a disconnect in social music sharing experiences for users who prefer this platform.

## 3. Objectives
- Develop a seamless browser extension for Brave that connects YouTube Music activity to Discord
- Extract comprehensive music information including song title, artist, album, artwork, timestamps, and playback status from YouTube Music
- Transmit this data to Discord's Rich Presence API in real-time
- Provide users with customization options for their Rich Presence display
- Ensure minimal performance impact on both the browser and Discord client

## 4. Deliverables
- A fully functional browser extension compatible with Brave
- Extension package ready for distribution
- Source code repository with documentation
- User guide for installation and customization
- Privacy policy and terms of service documentation

## 5. Scope
### Included:
- Support for YouTube Music web player
- Real-time song information updates
- Album artwork integration
- Playback status detection (playing, paused)
- Elapsed and total time display
- User customization settings
- Support for private listening mode

### Excluded:
- Support for standard YouTube videos
- Integration with other music streaming services
- Mobile device support
- Modification of YouTube Music's user interface or functionality
- Audio capture or manipulation

## 6. Constraints
- Discord Rich Presence API limitations (update frequency, character limits)
- Brave browser extension security policies and permissions
- YouTube Music's web structure and potential future changes to their interface
- Cross-platform compatibility challenges
- User privacy considerations and data handling restrictions

## 7. Assumptions
- Users have both Discord and Brave browser installed
- Discord is running while using YouTube Music
- Users have accounts for both services
- YouTube Music's web player structure remains relatively stable
- Brave's extension capabilities remain compatible with the implementation approach
- Users have stable internet connectivity for real-time updates

## 8. Inputs
- YouTube Music web player DOM structure
- Song metadata (title, artist, album, duration)
- Album artwork URL
- Playback state information
- User configuration preferences
- Discord Rich Presence API documentation
- Brave browser extension API documentation

## 9. Outputs
- Discord Rich Presence display showing:
  - Song title and artist name
  - Album name
  - Album artwork
  - Elapsed/remaining time
  - Play/pause status
  - YouTube Music logo/branding
- Extension icon state indicators (active/inactive)
- User settings interface
- Status notifications
- Error handling and troubleshooting information