@echo off
echo ========================================
echo  YouTube Music Enhanced Discord RPC
echo ========================================
echo.
echo Checking dependencies...

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install discord-rpc ws
    if errorlevel 1 (
        echo.
        echo ❌ Failed to install dependencies!
        echo Make sure Node.js and npm are installed.
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully!
    echo.
)

echo Starting WebSocket server with enhanced features...
echo.
echo Features:
echo - 🖼️ Song thumbnails as large images
echo - ⏱️ Real-time progress bar (like Spotify)
echo - 🎵 Enhanced formatting with emojis
echo - 🔗 Two clickable buttons
echo - 🚀 Modern visual effects
echo.
echo Server will start on localhost:8765
echo Make sure to reload your Chrome extension after starting!
echo.
echo Press any key to start the server...
pause >nul
echo.
echo 🚀 Starting server...
node websocket-host.js
echo.
echo Server stopped. Press any key to exit...
pause >nul
