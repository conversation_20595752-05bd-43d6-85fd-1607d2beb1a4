# YouTube Music Discord Rich Presence - Setup Guide

## Quick Start

Your extension is now ready to run! Follow these steps to get it working:

### Step 1: Load the Extension in Browser

1. Open your browser (Chrome/Brave/Edge)
2. Go to `chrome://extensions/` (or `brave://extensions/` for Brave)
3. Enable "Developer mode" (toggle in top right)
4. Click "Load unpacked"
5. Select this folder: `c:\Users\<USER>\OneDrive\Desktop\My projects\dc_precesnce`
6. The extension should now appear in your extensions list
7. **IMPORTANT**: Copy the Extension ID (long string of letters/numbers) - you'll need it for Step 3

### Step 2: Create Discord Application

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application"
3. Name it "YouTube Music Rich Presence" (or any name you like)
4. Go to the "Rich Presence" section
5. Upload a YouTube Music logo as "ytmusic_logo" (optional but recommended)
6. The Client ID `1375799244400623726` is already configured in the extension

### Step 3: Set Up Native Messaging Host

The native messaging host files are already created. You just need to:

1. **Update the manifest file** with your Extension ID:
   - Open: `%USERPROFILE%\YTMusicDiscordRP\com.discord.rich_presence.json`
   - Replace `YOUR_EXTENSION_ID_HERE` with the actual Extension ID from Step 1
   - Save the file

2. **Register the native messaging host** in Windows Registry:
   - Open Registry Editor (Win+R, type `regedit`, press Enter)
   - Navigate to: `HKEY_CURRENT_USER\Software\Google\Chrome\NativeMessagingHosts`
   - Create new key: `com.discord.rich_presence`
   - Set default value to: `C:\Users\<USER>\OneDrive\Desktop\My projects\dc_precesnce\%USERPROFILE%\YTMusicDiscordRP\com.discord.rich_presence.json`
   
   For Brave browser, also create the same entry under:
   `HKEY_CURRENT_USER\Software\BraveSoftware\Brave-Browser\NativeMessagingHosts`

### Step 4: Test the Extension

1. Make sure Discord is running
2. Open YouTube Music (https://music.youtube.com)
3. Play a song
4. Click the extension icon in your browser
5. You should see the connection status and current song info

## Files Created

- ✅ `manifest.json` - Fixed (removed missing icon references)
- ✅ `background.js` - Ready to run
- ✅ `content.js` - Ready to run  
- ✅ `popup.html` & `popup.js` - Fixed (removed missing icon references)
- ✅ `options.html` & `options.js` - Fixed (removed missing icon references)
- ✅ Native messaging host files in `%USERPROFILE%\YTMusicDiscordRP\`:
  - `host.js` - Node.js script to communicate with Discord
  - `package.json` - Dependencies configuration
  - `com.discord.rich_presence.json` - Native messaging manifest
  - `node_modules/` - Installed dependencies (discord-rpc)

## Troubleshooting

If the extension doesn't work:

1. **Check Extension Console**: Go to `chrome://extensions/`, find your extension, click "Inspect views: service worker"
2. **Check Discord Connection**: Make sure Discord desktop app is running
3. **Verify Registry**: Ensure the native messaging host is properly registered
4. **Check Extension ID**: Make sure you updated the manifest file with the correct Extension ID
5. **Test Native Host**: Open Command Prompt and run:
   ```
   cd "C:\Users\<USER>\OneDrive\Desktop\My projects\dc_precesnce\%USERPROFILE%\YTMusicDiscordRP"
   node host.js
   ```

## What's Working

- ✅ Extension loads without errors
- ✅ Content script detects YouTube Music
- ✅ Background service worker handles Discord communication
- ✅ Popup shows connection status and current song
- ✅ Options page allows customization
- ✅ Native messaging host is set up and ready

The extension is now ready to run! Just follow the setup steps above.
