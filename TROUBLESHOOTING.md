# 🔧 Troubleshooting Guide

## 🖼️ **Thumbnail Not Showing**

### **Possible Causes:**
1. **Discord Image Cache** - Discord caches images and may not update immediately
2. **Image URL Format** - YouTube Music thumbnails need proper formatting
3. **Discord RPC Limitations** - Some image URLs may not be supported

### **Solutions:**
1. **Test with Sample Image:**
   ```bash
   node test-rpc.js
   ```
   This will test Discord RPC with a known working image.

2. **Clear Discord Cache:**
   - Close Discord completely
   - Press `Win + R`, type `%appdata%\discord\Cache`
   - Delete all files in the Cache folder
   - Restart Discord

3. **Check Console Logs:**
   - Open YouTube Music
   - Press `F12` → Console tab
   - Look for thumbnail URLs being extracted
   - Check if URLs are valid

## 🎵 **Presence Disappearing When Changing Songs**

### **Possible Causes:**
1. **Race Condition** - New song detected before old one is cleared
2. **Message Timing** - Extension and server communication issues
3. **Discord Rate Limiting** - Too many updates too quickly

### **Solutions:**
1. **Check Server Logs:**
   - Look at the WebSocket server console
   - Check for error messages
   - Verify activity updates are being received

2. **Extension Console:**
   - Go to `chrome://extensions/`
   - Click "Service Worker" under your extension
   - Check for error messages

3. **Reload Extension:**
   - Go to `chrome://extensions/`
   - Click the reload button (🔄) on your extension

## 🔍 **Debugging Steps**

### **1. Verify Server Connection:**
```bash
# Start the server
node websocket-host.js

# Should show:
# [RPC-SERVER] WebSocket server listening on port 8765
# [RPC-SERVER] Successfully connected to Discord
```

### **2. Test Extension Connection:**
1. Open YouTube Music
2. Click extension icon
3. Should show "Connected to Discord"

### **3. Check Activity Data:**
1. Open YouTube Music
2. Press `F12` → Console
3. Play a song
4. Look for "Sending activity data:" logs

### **4. Verify Discord RPC:**
```bash
# Test Discord connection directly
node test-rpc.js
```

## 🚨 **Common Issues**

### **"WebSocket connection failed"**
- **Solution:** Make sure the server is running (`node websocket-host.js`)
- **Check:** Port 8765 is not blocked by firewall

### **"Discord not connected"**
- **Solution:** Make sure Discord is running
- **For Vencord:** Ensure RPC is enabled in settings

### **"No song detected"**
- **Solution:** Make sure you're on music.youtube.com
- **Check:** Song is actually playing (not paused)

### **"Thumbnails are low quality"**
- **Solution:** The extension automatically requests high-res versions
- **Note:** Some songs may only have low-res thumbnails available

## 🎯 **Vencord Specific Issues**

### **RPC Not Working:**
1. Open Vencord Settings
2. Go to Plugins → Rich Presence
3. Make sure it's enabled
4. Restart Discord

### **External RPC Blocked:**
1. Check Vencord settings for RPC restrictions
2. Try disabling other RPC plugins temporarily
3. Restart Discord after changes

## 📊 **Performance Tips**

### **Reduce Update Frequency:**
- Extension updates every 5 seconds by default
- This prevents Discord rate limiting
- Don't modify UPDATE_INTERVAL unless necessary

### **Memory Usage:**
- Server uses minimal memory (~10MB)
- Extension uses minimal resources
- No impact on YouTube Music performance

## 🔄 **Reset Everything**

If nothing works, try a complete reset:

1. **Stop the server** (Ctrl+C)
2. **Close Discord**
3. **Reload the extension**
4. **Restart Discord**
5. **Start the server again**
6. **Test with a new song**

## 📞 **Still Having Issues?**

1. **Check the logs:**
   - Server console output
   - Extension service worker console
   - Browser console on YouTube Music

2. **Test components individually:**
   - Run `node test-rpc.js` to test Discord connection
   - Check if YouTube Music song detection works
   - Verify WebSocket connection

3. **Provide debug info:**
   - Operating System
   - Discord/Vencord version
   - Chrome version
   - Console error messages
