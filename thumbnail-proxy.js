// Thumbnail Proxy Service for Discord Rich Presence
// This service converts YouTube thumbnails to Discord-compatible URLs

const express = require('express');
const https = require('https');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const app = express();
const PORT = 3000;
const CACHE_DIR = path.join(__dirname, 'thumbnail_cache');

// Create cache directory
if (!fs.existsSync(CACHE_DIR)) {
  fs.mkdirSync(CACHE_DIR);
}

// Enable CORS for Discord
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  next();
});

// Serve cached thumbnails
app.use('/thumbnails', express.static(CACHE_DIR));

// Main thumbnail proxy endpoint
app.get('/proxy/:url', async (req, res) => {
  try {
    const originalUrl = decodeURIComponent(req.params.url);
    console.log('Proxying thumbnail:', originalUrl);

    // Create a hash of the URL for caching
    const urlHash = crypto.createHash('md5').update(originalUrl).digest('hex');
    const cacheFile = path.join(CACHE_DIR, `${urlHash}.jpg`);

    // Check if cached version exists
    if (fs.existsSync(cacheFile)) {
      console.log('Serving cached thumbnail');
      return res.sendFile(cacheFile);
    }

    // Download and cache the image
    const imageData = await downloadImage(originalUrl);
    
    // Save to cache
    fs.writeFileSync(cacheFile, imageData);
    
    // Serve the image
    res.contentType('image/jpeg');
    res.send(imageData);
    
    console.log('Thumbnail cached and served');
  } catch (error) {
    console.error('Error proxying thumbnail:', error);
    res.status(500).json({ error: 'Failed to proxy thumbnail' });
  }
});

// Download image from URL
function downloadImage(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`HTTP ${response.statusCode}`));
        return;
      }

      const chunks = [];
      response.on('data', (chunk) => chunks.push(chunk));
      response.on('end', () => resolve(Buffer.concat(chunks)));
      response.on('error', reject);
    }).on('error', reject);
  });
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    service: 'YouTube Music Thumbnail Proxy',
    cached_images: fs.readdirSync(CACHE_DIR).length
  });
});

// Start server
app.listen(PORT, () => {
  console.log('🖼️  YouTube Music Thumbnail Proxy Server');
  console.log('==========================================');
  console.log(`Server running on http://localhost:${PORT}`);
  console.log('');
  console.log('Usage:');
  console.log(`http://localhost:${PORT}/proxy/[ENCODED_YOUTUBE_THUMBNAIL_URL]`);
  console.log('');
  console.log('Example:');
  console.log(`http://localhost:${PORT}/proxy/https%3A//i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg`);
  console.log('');
  console.log('This creates Discord-compatible URLs for YouTube thumbnails!');
});

module.exports = app;
