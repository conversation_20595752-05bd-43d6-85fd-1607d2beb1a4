// Debug script for YouTube Music Discord Rich Presence Extension
// Run this in the browser console on YouTube Music to test song detection

console.log("🎵 YouTube Music Discord Rich Presence Debug Tool");
console.log("================================================");

// Test song info extraction
function testSongExtraction() {
  console.log("\n🔍 Testing song information extraction...");
  
  // Multiple selectors for song title
  const titleSelectors = [
    'ytmusic-player-bar .title.ytmusic-player-bar',
    '.ytmusic-player-bar .title',
    'ytmusic-player-bar .content-info-wrapper .title',
    '#layout-player .title',
    '.player-bar-wrapper .title'
  ];
  
  let title = "";
  for (const selector of titleSelectors) {
    const element = document.querySelector(selector);
    if (element && element.textContent.trim()) {
      title = element.textContent.trim();
      console.log(`✅ Title found with selector: ${selector}`);
      console.log(`   Title: "${title}"`);
      break;
    } else {
      console.log(`❌ No title found with selector: ${selector}`);
    }
  }
  
  // Multiple selectors for artist name
  const artistSelectors = [
    'ytmusic-player-bar .byline a[href*="/channel/"]',
    'ytmusic-player-bar .byline .yt-simple-endpoint',
    '.ytmusic-player-bar .byline a',
    'ytmusic-player-bar .subtitle a',
    '.player-bar-wrapper .byline a'
  ];
  
  let artist = "";
  for (const selector of artistSelectors) {
    const element = document.querySelector(selector);
    if (element && element.textContent.trim()) {
      artist = element.textContent.trim();
      console.log(`✅ Artist found with selector: ${selector}`);
      console.log(`   Artist: "${artist}"`);
      break;
    } else {
      console.log(`❌ No artist found with selector: ${selector}`);
    }
  }
  
  // Test playback state
  console.log("\n🎮 Testing playback state detection...");
  const buttonSelectors = [
    'ytmusic-player-bar .play-pause-button',
    'ytmusic-player-bar tp-yt-paper-icon-button[data-title-no-tooltip]',
    '.ytmusic-player-bar [aria-label*="Play"], .ytmusic-player-bar [aria-label*="Pause"]',
    '#play-pause-button',
    '.player-controls .play-pause-button'
  ];
  
  let playbackState = "unknown";
  for (const selector of buttonSelectors) {
    const button = document.querySelector(selector);
    if (button) {
      const title = button.getAttribute('title') || '';
      const ariaLabel = button.getAttribute('aria-label') || '';
      const dataTitle = button.getAttribute('data-title-no-tooltip') || '';
      
      const buttonState = (title + ' ' + ariaLabel + ' ' + dataTitle).toLowerCase();
      
      console.log(`✅ Play/pause button found with selector: ${selector}`);
      console.log(`   Button attributes: title="${title}", aria-label="${ariaLabel}", data-title="${dataTitle}"`);
      
      if (buttonState.includes('pause')) {
        playbackState = 'playing';
      } else if (buttonState.includes('play')) {
        playbackState = 'paused';
      }
      break;
    } else {
      console.log(`❌ No play/pause button found with selector: ${selector}`);
    }
  }
  
  console.log(`   Detected playback state: ${playbackState}`);
  
  // Test album art
  console.log("\n🖼️ Testing album art detection...");
  const artSelectors = [
    'ytmusic-player-bar .image',
    'ytmusic-player-bar img',
    '.player-bar-wrapper .image',
    '#layout-player .image img',
    'ytmusic-player-bar .thumbnail img'
  ];
  
  let albumArt = "";
  for (const selector of artSelectors) {
    const element = document.querySelector(selector);
    if (element && element.src) {
      albumArt = element.src;
      console.log(`✅ Album art found with selector: ${selector}`);
      console.log(`   URL: ${albumArt}`);
      break;
    } else {
      console.log(`❌ No album art found with selector: ${selector}`);
    }
  }
  
  // Summary
  console.log("\n📊 SUMMARY");
  console.log("==========");
  console.log(`Title: ${title || "❌ NOT FOUND"}`);
  console.log(`Artist: ${artist || "❌ NOT FOUND"}`);
  console.log(`Playback State: ${playbackState}`);
  console.log(`Album Art: ${albumArt ? "✅ FOUND" : "❌ NOT FOUND"}`);
  
  return {
    title,
    artist,
    playbackState,
    albumArt
  };
}

// Test extension communication
function testExtensionCommunication() {
  console.log("\n📡 Testing extension communication...");
  
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    console.log("✅ Chrome extension API available");
    
    // Test background script communication
    chrome.runtime.sendMessage({ type: 'getConnectionStatus' }, (response) => {
      if (chrome.runtime.lastError) {
        console.log("❌ Error communicating with background script:", chrome.runtime.lastError);
      } else {
        console.log("✅ Background script communication successful");
        console.log("   Response:", response);
      }
    });
  } else {
    console.log("❌ Chrome extension API not available");
  }
}

// Test DOM structure
function inspectPlayerStructure() {
  console.log("\n🔍 Inspecting player DOM structure...");
  
  const playerBar = document.querySelector('ytmusic-player-bar');
  if (playerBar) {
    console.log("✅ Player bar found");
    console.log("   Classes:", playerBar.className);
    console.log("   Children:", Array.from(playerBar.children).map(child => child.tagName + (child.className ? '.' + child.className : '')));
  } else {
    console.log("❌ Player bar not found");
  }
  
  // List all potential player-related elements
  const playerElements = document.querySelectorAll('[class*="player"], [class*="ytmusic"], [id*="player"]');
  console.log(`\n📋 Found ${playerElements.length} player-related elements:`);
  playerElements.forEach((el, index) => {
    console.log(`   ${index + 1}. ${el.tagName}${el.id ? '#' + el.id : ''}${el.className ? '.' + el.className.split(' ').join('.') : ''}`);
  });
}

// Run all tests
function runAllTests() {
  console.clear();
  console.log("🎵 YouTube Music Discord Rich Presence Debug Tool");
  console.log("================================================");
  
  inspectPlayerStructure();
  const songData = testSongExtraction();
  testExtensionCommunication();
  
  console.log("\n🎯 Quick Test Result:");
  console.log("=====================");
  if (songData.title && songData.artist) {
    console.log("✅ Song detection is working!");
  } else {
    console.log("❌ Song detection needs fixing");
    console.log("💡 Try updating the DOM selectors in content.js");
  }
  
  return songData;
}

// Auto-run tests
runAllTests();

// Make functions available globally for manual testing
window.debugYTMusic = {
  runAllTests,
  testSongExtraction,
  testExtensionCommunication,
  inspectPlayerStructure
};

console.log("\n💡 Available commands:");
console.log("   debugYTMusic.runAllTests() - Run all tests");
console.log("   debugYTMusic.testSongExtraction() - Test song detection only");
console.log("   debugYTMusic.testExtensionCommunication() - Test extension communication");
console.log("   debugYTMusic.inspectPlayerStructure() - Inspect DOM structure");
