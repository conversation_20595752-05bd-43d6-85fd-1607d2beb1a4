<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>YouTube Music Discord Rich Presence - Settings</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      color: #e8e8e8;
      background-color: #36393f;
      line-height: 1.6;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 30px;
    }

    .header {
      display: flex;
      align-items: center;
      margin-bottom: 30px;
    }

    .logo {
      width: 64px;
      height: 64px;
      margin-right: 20px;
    }

    h1 {
      font-size: 24px;
      font-weight: 600;
      margin: 0;
      color: #fff;
    }

    .subheading {
      color: #b9bbbe;
      margin-top: 5px;
      font-size: 16px;
    }

    .section {
      background-color: #2f3136;
      border-radius: 5px;
      padding: 20px;
      margin-bottom: 25px;
    }

    h2 {
      font-size: 18px;
      margin-top: 0;
      margin-bottom: 15px;
      color: #fff;
      border-bottom: 1px solid #4f545c;
      padding-bottom: 10px;
    }

    .setting-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15px;
      padding-bottom: 15px;
      border-bottom: 1px solid #40444b;
    }
    .setting-row:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }

    .setting-info {
      flex: 1;
    }

    .setting-title {
      font-weight: 600;
      margin-bottom: 5px;
    }

    .setting-description {
      color: #b9bbbe;
      font-size: 14px;
    }

    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 50px;
      height: 24px;
    }

    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .toggle-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #72767d;
      transition: .3s;
      border-radius: 24px;
    }

    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .3s;
      border-radius: 50%;
    }

    input:checked + .toggle-slider {
      background-color: #7289da;
    }

    input:checked + .toggle-slider:before {
      transform: translateX(26px);
    }

    .button-container {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
    }

    button {
      background-color: #4f545c;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 10px 15px;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.2s;
      margin-left: 10px;
    }

    button:hover {
      background-color: #5d6269;
    }

    button.primary {
      background-color: #7289da;
    }

    button.primary:hover {
      background-color: #8296e0;
    }

    .preview-container {
      background-color: #202225;
      border-radius: 5px;
      padding: 20px;
      margin-top: 20px;
    }

    .preview-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 15px;
      color: #fff;
    }

    .discord-preview {
      background-color: #18191c;
      border-radius: 3px;
      padding: 15px;
      font-size: 14px;
    }

    .playing-status {
      color: #b9bbbe;
      font-size: 12px;
      margin-bottom: 10px;
    }

    .activity-card {
      display: flex;
      margin-bottom: 15px;
    }

    .activity-image {
      width: 60px;
      height: 60px;
      border-radius: 3px;
      margin-right: 15px;
      background-color: #2f3136;
      object-fit: cover;
    }

    .activity-details {
      flex: 1;
    }

    .activity-name {
      color: #fff;
      font-weight: 600;
      margin-bottom: 5px;
    }

    .activity-artist {
      color: #b9bbbe;
      margin-bottom: 5px;
    }

    .activity-time {
      color: #b9bbbe;
      font-size: 12px;
    }

    .activity-buttons {
      margin-top: 10px;
    }

    .activity-button {
      display: inline-block;
      background-color: #4f545c;
      color: #fff;
      border-radius: 3px;
      padding: 5px 10px;
      font-size: 12px;
      margin-right: 8px;
    }

    .footer {
      margin-top: 30px;
      text-align: center;
      color: #b9bbbe;
      font-size: 13px;
    }

    .footer a {
      color: #7289da;
      text-decoration: none;
    }

    .footer a:hover {
      text-decoration: underline;
    }

    .status-info {
      display: flex;
      align-items: center;
      background-color: #202225;
      border-radius: 4px;
      padding: 10px 15px;
      margin-bottom: 20px;
    }

    .status-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 10px;
    }

    .status-dot.connected {
      background-color: #43b581;
    }

    .status-dot.disconnected {
      background-color: #f04747;
    }

    .connection-info {
      margin-left: auto;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo" style="background-color: #ff0000; border-radius: 8px;"></div>
      <div>
        <h1>YouTube Music Discord Rich Presence</h1>
        <div class="subheading">Settings</div>
      </div>
    </div>

    <div class="status-info">
      <div id="status-dot" class="status-dot disconnected"></div>
      <div id="status-text">Checking Discord connection status...</div>
      <div class="connection-info">
        <button id="reconnect-button">Reconnect</button>
      </div>
    </div>

    <div class="section">
      <h2>General Settings</h2>

      <div class="setting-row">
        <div class="setting-info">
          <div class="setting-title">Enable Rich Presence</div>
          <div class="setting-description">Display your YouTube Music activity in Discord</div>
        </div>
        <label class="toggle-switch">
          <input type="checkbox" id="enable-presence" checked>
          <span class="toggle-slider"></span>
        </label>
      </div>

      <div class="setting-row">
        <div class="setting-info">
          <div class="setting-title">Private Mode</div>
          <div class="setting-description">Hide song details and only show that you're listening to YouTube Music</div>
        </div>
        <label class="toggle-switch">
          <input type="checkbox" id="private-mode">
          <span class="toggle-slider"></span>
        </label>
      </div>

      <div class="setting-row">
        <div class="setting-info">
          <div class="setting-title">Auto Connect</div>
          <div class="setting-description">Automatically connect to Discord when YouTube Music is open</div>
        </div>
        <label class="toggle-switch">
          <input type="checkbox" id="auto-connect" checked>
          <span class="toggle-slider"></span>
        </label>
      </div>
    </div>

    <div class="section">
      <h2>Display Settings</h2>

      <div class="setting-row">
        <div class="setting-info">
          <div class="setting-title">Show Song Title</div>
          <div class="setting-description">Display the current song title in your Discord presence</div>
        </div>
        <label class="toggle-switch">
          <input type="checkbox" id="show-title" checked>
          <span class="toggle-slider"></span>
        </label>
      </div>

      <div class="setting-row">
        <div class="setting-info">
          <div class="setting-title">Show Artist</div>
          <div class="setting-description">Display the artist name in your Discord presence</div>
        </div>
        <label class="toggle-switch">
          <input type="checkbox" id="show-artist" checked>
          <span class="toggle-slider"></span>
        </label>
      </div>

      <div class="setting-row">
        <div class="setting-info">
          <div class="setting-title">Show Album</div>
          <div class="setting-description">Display the album name in your Discord presence</div>
        </div>
        <label class="toggle-switch">
          <input type="checkbox" id="show-album" checked>
          <span class="toggle-slider"></span>
        </label>
      </div>

      <div class="setting-row">
        <div class="setting-info">
          <div class="setting-title">Show Album Artwork</div>
          <div class="setting-description">Display the album artwork in your Discord presence</div>
        </div>
        <label class="toggle-switch">
          <input type="checkbox" id="show-artwork" checked>
          <span class="toggle-slider"></span>
        </label>
      </div>

      <div class="setting-row">
        <div class="setting-info">
          <div class="setting-title">Show Timestamps</div>
          <div class="setting-description">Display elapsed and total time in your Discord presence</div>
        </div>
        <label class="toggle-switch">
          <input type="checkbox" id="show-time" checked>
          <span class="toggle-slider"></span>
        </label>
      </div>

      <div class="setting-row">
        <div class="setting-info">
          <div class="setting-title">Show Listen Button</div>
          <div class="setting-description">Add a button that others can click to listen to the same song</div>
        </div>
        <label class="toggle-switch">
          <input type="checkbox" id="show-button" checked>
          <span class="toggle-slider"></span>
        </label>
      </div>

      <div class="preview-container">
        <div class="preview-title">Preview</div>
        <div class="discord-preview">
          <div class="playing-status">Playing YouTube Music</div>
          <div class="activity-card">
            <img src="img-proxy-1.webp" alt="Album Art" class="activity-image">
            <div class="activity-details">
              <div class="activity-name">Song Title</div>
              <div class="activity-artist">by Artist</div>
              <div class="activity-time">3:45 elapsed</div>
            </div>
          </div>
          <div class="activity-buttons">
            <div class="activity-button">Listen on YouTube Music</div>
          </div>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>Advanced Settings</h2>

      <div class="setting-row">
        <div class="setting-info">
          <div class="setting-title">Update Frequency</div>
          <div class="setting-description">How often to update your Discord presence (lower values may affect performance)</div>
        </div>
        <select id="update-frequency">
          <option value="15000">Normal (15s)</option>
          <option value="10000">Frequent (10s)</option>
          <option value="5000">Very Frequent (5s)</option>
        </select>
      </div>
    </div>

    <div class="button-container">
      <button id="reset-button">Reset to Defaults</button>
      <button id="save-button" class="primary">Save Settings</button>
    </div>

    <div class="footer">
      <p>YouTube Music Discord Rich Presence v1.0.0</p>
      <p>Created with ❤️ by <a href="#" target="_blank">Your Name</a></p>
      <p>
        <a href="#" target="_blank">GitHub</a> |
        <a href="#" target="_blank">Support</a> |
        <a href="#" target="_blank">Privacy Policy</a>
      </p>
    </div>
  </div>

  <script src="options.js"></script>
</body>
</html>