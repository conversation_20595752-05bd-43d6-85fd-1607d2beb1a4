// Complete Thumbnail Service for Discord Rich Presence
// Combines multiple methods to get song thumbnails working in Discord

const express = require('express');
const https = require('https');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class ThumbnailService {
  constructor() {
    this.app = express();
    this.PORT = 3000;
    this.CACHE_DIR = path.join(__dirname, 'thumbnail_cache');
    this.cache = new Map();
    
    this.setupServer();
    this.createCacheDir();
  }

  createCacheDir() {
    if (!fs.existsSync(this.CACHE_DIR)) {
      fs.mkdirSync(this.CACHE_DIR);
    }
  }

  setupServer() {
    // Enable CORS
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
      next();
    });

    // Serve cached thumbnails
    this.app.use('/thumbnails', express.static(this.CACHE_DIR));

    // Main proxy endpoint
    this.app.get('/proxy/:url', this.handleProxyRequest.bind(this));

    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        service: 'YouTube Music Thumbnail Service',
        cached_images: fs.readdirSync(this.CACHE_DIR).length,
        methods: ['proxy', 'cache', 'fallback']
      });
    });

    // Get thumbnail info
    this.app.get('/info/:url', this.handleInfoRequest.bind(this));
  }

  async handleProxyRequest(req, res) {
    try {
      const originalUrl = decodeURIComponent(req.params.url);
      console.log('🖼️  Processing thumbnail:', originalUrl);

      // Try different methods in order of preference
      const result = await this.getThumbnailUrl(originalUrl);
      
      if (result.success) {
        if (result.type === 'cached') {
          return res.sendFile(result.path);
        } else if (result.type === 'data') {
          res.contentType('image/jpeg');
          return res.send(result.data);
        } else if (result.type === 'redirect') {
          return res.redirect(result.url);
        }
      }

      // Fallback to error image
      res.status(404).json({ error: 'Thumbnail not available' });
      
    } catch (error) {
      console.error('❌ Error processing thumbnail:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  async handleInfoRequest(req, res) {
    const originalUrl = decodeURIComponent(req.params.url);
    const urlHash = crypto.createHash('md5').update(originalUrl).digest('hex');
    const cacheFile = path.join(this.CACHE_DIR, `${urlHash}.jpg`);
    
    res.json({
      original_url: originalUrl,
      hash: urlHash,
      cached: fs.existsSync(cacheFile),
      proxy_url: `http://localhost:${this.PORT}/proxy/${encodeURIComponent(originalUrl)}`,
      cache_file: cacheFile
    });
  }

  async getThumbnailUrl(originalUrl) {
    const urlHash = crypto.createHash('md5').update(originalUrl).digest('hex');
    const cacheFile = path.join(this.CACHE_DIR, `${urlHash}.jpg`);

    // Method 1: Check local cache
    if (fs.existsSync(cacheFile)) {
      console.log('✅ Using cached thumbnail');
      return {
        success: true,
        type: 'cached',
        path: cacheFile
      };
    }

    // Method 2: Download and cache
    try {
      console.log('📥 Downloading thumbnail...');
      const imageData = await this.downloadImage(originalUrl);
      
      // Save to cache
      fs.writeFileSync(cacheFile, imageData);
      console.log('✅ Thumbnail cached');
      
      return {
        success: true,
        type: 'data',
        data: imageData
      };
    } catch (error) {
      console.error('❌ Download failed:', error.message);
    }

    // Method 3: Try alternative URL formats
    const alternativeUrls = this.getAlternativeUrls(originalUrl);
    for (const altUrl of alternativeUrls) {
      try {
        console.log('🔄 Trying alternative URL:', altUrl);
        const imageData = await this.downloadImage(altUrl);
        
        // Save to cache
        fs.writeFileSync(cacheFile, imageData);
        console.log('✅ Alternative URL worked');
        
        return {
          success: true,
          type: 'data',
          data: imageData
        };
      } catch (error) {
        console.log('❌ Alternative failed:', error.message);
      }
    }

    return { success: false };
  }

  getAlternativeUrls(originalUrl) {
    const alternatives = [];
    
    // YouTube thumbnail alternatives
    if (originalUrl.includes('i.ytimg.com')) {
      const videoId = this.extractVideoId(originalUrl);
      if (videoId) {
        alternatives.push(
          `https://i.ytimg.com/vi/${videoId}/maxresdefault.jpg`,
          `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`,
          `https://i.ytimg.com/vi/${videoId}/mqdefault.jpg`,
          `https://i.ytimg.com/vi/${videoId}/default.jpg`
        );
      }
    }

    return alternatives;
  }

  extractVideoId(url) {
    const match = url.match(/\/vi\/([^\/]+)\//);
    return match ? match[1] : null;
  }

  downloadImage(url) {
    return new Promise((resolve, reject) => {
      const request = https.get(url, (response) => {
        if (response.statusCode === 302 || response.statusCode === 301) {
          // Follow redirect
          return this.downloadImage(response.headers.location)
            .then(resolve)
            .catch(reject);
        }

        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}`));
          return;
        }

        const chunks = [];
        response.on('data', (chunk) => chunks.push(chunk));
        response.on('end', () => resolve(Buffer.concat(chunks)));
        response.on('error', reject);
      });

      request.on('error', reject);
      request.setTimeout(10000, () => {
        request.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  start() {
    this.app.listen(this.PORT, () => {
      console.log('🖼️  YouTube Music Thumbnail Service');
      console.log('====================================');
      console.log(`✅ Server running on http://localhost:${this.PORT}`);
      console.log('');
      console.log('🎯 Features:');
      console.log('  • Local caching for fast access');
      console.log('  • Multiple fallback methods');
      console.log('  • Discord-compatible URLs');
      console.log('  • Automatic retry with alternatives');
      console.log('');
      console.log('🔗 Usage:');
      console.log(`  http://localhost:${this.PORT}/proxy/[ENCODED_URL]`);
      console.log('');
      console.log('Ready to serve thumbnails! 🚀');
    });
  }
}

// Start the service
if (require.main === module) {
  const service = new ThumbnailService();
  service.start();
}

module.exports = ThumbnailService;
