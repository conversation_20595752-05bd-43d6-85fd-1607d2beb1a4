<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>YouTube Music Discord Rich Presence</title>
  <style>
    body {
      width: 320px;
      font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 15px;
      color: #e8e8e8;
      background-color: #36393f;
      font-size: 14px;
    }

    .header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #4f545c;
    }

    .logo {
      width: 24px;
      height: 24px;
      margin-right: 10px;
    }

    h1 {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      color: #fff;
    }

    .status-indicator {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      padding: 10px;
      border-radius: 4px;
      background-color: #2f3136;
    }

    .status-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 10px;
    }

    .status-dot.connected {
      background-color: #43b581;
    }

    .status-dot.disconnected {
      background-color: #f04747;
    }

    .status-text {
      font-weight: 500;
    }

    .now-playing {
      padding: 10px;
      margin-bottom: 15px;
      background-color: #2f3136;
      border-radius: 4px;
    }

    .now-playing.empty {
      color: #b9bbbe;
      text-align: center;
      padding: 15px;
    }

    .song-info {
      display: flex;
      margin-bottom: 10px;
    }

    .album-art {
      width: 60px;
      height: 60px;
      border-radius: 3px;
      margin-right: 10px;
      background-color: #202225;
      object-fit: cover;
    }

    .song-details {
      flex: 1;
      overflow: hidden;
    }

    .song-title {
      font-weight: 600;
      color: #fff;
      margin-bottom: 5px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .song-artist {
      color: #b9bbbe;
      margin-bottom: 5px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .song-album {
      color: #b9bbbe;
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .playback-indicator {
      display: inline-block;
      padding: 3px 5px;
      border-radius: 3px;
      background-color: #43b581;
      color: white;
      font-size: 11px;
      font-weight: 500;
    }

    .playback-indicator.paused {
      background-color: #f04747;
    }

    .controls {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
    }

    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 46px;
      height: 24px;
    }

    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .toggle-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #72767d;
      transition: .3s;
      border-radius: 24px;
    }

    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .3s;
      border-radius: 50%;
    }

    input:checked + .toggle-slider {
      background-color: #7289da;
    }

    input:checked + .toggle-slider:before {
      transform: translateX(22px);
    }

    .control-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
    }

    .control-label {
      font-weight: 500;
    }

    .button-container {
      display: flex;
      justify-content: space-between;
    }

    button {
      background-color: #4f545c;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 12px;
      font-size: 13px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    button:hover {
      background-color: #5d6269;
    }

    button.primary {
      background-color: #7289da;
    }

    button.primary:hover {
      background-color: #8296e0;
    }

    .footer {
      margin-top: 15px;
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #b9bbbe;
    }

    .footer a {
      color: #7289da;
      text-decoration: none;
    }

    .footer a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo" style="background-color: #ff0000; border-radius: 3px;"></div>
    <h1>YouTube Music Rich Presence</h1>
  </div>

  <div class="status-indicator">
    <div id="status-dot" class="status-dot disconnected"></div>
    <div id="status-text" class="status-text">Disconnected from Discord</div>
  </div>

  <div id="now-playing-container" class="now-playing empty">
    Not playing anything on YouTube Music
  </div>

  <div class="controls">
    <div class="control-item">
      <span class="control-label">Enable Rich Presence</span>
      <label class="toggle-switch">
        <input type="checkbox" id="enable-presence" checked>
        <span class="toggle-slider"></span>
      </label>
    </div>

    <div class="control-item">
      <span class="control-label">Private Mode</span>
      <label class="toggle-switch">
        <input type="checkbox" id="private-mode">
        <span class="toggle-slider"></span>
      </label>
    </div>
  </div>

  <div class="button-container">
    <button id="options-button">Open Settings</button>
    <button id="reconnect-button" class="primary">Reconnect</button>
  </div>

  <div class="footer">
    <span>v1.0.0</span>
    <a href="#" id="support-link">Need help?</a>
  </div>

  <script src="popup.js"></script>
</body>
</html>