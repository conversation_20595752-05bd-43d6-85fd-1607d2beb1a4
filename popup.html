<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>YouTube Music Discord Rich Presence</title>
  <style>
    body {
      width: 320px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 15px;
      color: #e8e8e8;
      background-color: #36393f;
      font-size: 14px;
    }

    .header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #4f545c;
    }

    .logo {
      width: 24px;
      height: 24px;
      margin-right: 10px;
    }

    h1 {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      color: #fff;
    }

    .status-indicator {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      padding: 10px;
      border-radius: 4px;
      background-color: #2f3136;
    }

    .status-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 10px;
    }

    .status-dot.connected {
      background-color: #43b581;
    }

    .status-dot.disconnected {
      background-color: #f04747;
    }

    .status-text {
      font-weight: 500;
    }

    /* Enhanced Now Playing Section */
    .now-playing {
      margin-bottom: 15px;
      background: linear-gradient(135deg, #2f3136 0%, #36393f 100%);
      border-radius: 12px;
      border: 1px solid #40444b;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      padding: 0;
    }

    .now-playing.empty {
      color: #b9bbbe;
      text-align: center;
      padding: 15px;
    }

    /* Enhanced Song Info Container */
    .enhanced-song-info {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 12px;
    }

    /* Album Art Container with Rotation Animation */
    .album-art-container {
      position: relative;
      flex-shrink: 0;
    }

    .album-art {
      width: 70px;
      height: 70px;
      border-radius: 10px;
      object-fit: cover;
      background-color: #202225;
      transition: transform 0.3s ease;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
    }

    .album-art.rotating {
      animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .playback-overlay {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.7);
      border-radius: 50%;
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .album-art-container:hover .playback-overlay {
      opacity: 1;
    }

    .playback-indicator.playing {
      color: #57f287;
    }

    .playback-indicator.paused {
      color: #ed4245;
    }

    /* Enhanced Song Details */
    .song-details {
      flex: 1;
      min-width: 0;
    }

    .song-title-container {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-bottom: 4px;
    }

    .song-title {
      font-weight: 600;
      color: #fff;
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
    }

    .status-indicators {
      display: flex;
      gap: 3px;
      font-size: 12px;
    }

    .song-artist {
      color: #b9bbbe;
      font-size: 13px;
      margin-bottom: 3px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-weight: 500;
    }

    .song-album {
      color: #72767d;
      font-size: 12px;
      margin-bottom: 3px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .playlist-info {
      color: #5865f2;
      font-size: 11px;
      margin-bottom: 6px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    /* Progress Bar */
    .progress-container {
      margin: 8px 0 6px 0;
    }

    .progress-bar {
      width: 100%;
      height: 3px;
      background: #40444b;
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: 3px;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #5865f2, #57f287);
      border-radius: 2px;
      transition: width 0.3s ease;
      position: relative;
    }

    .progress-fill::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 6px;
      height: 100%;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 2px;
      animation: pulse 2s ease-in-out infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 0.6; }
      50% { opacity: 1; }
    }

    .time-info {
      display: flex;
      justify-content: space-between;
      font-size: 10px;
      color: #72767d;
    }

    /* Queue and Metadata Info */
    .queue-info, .metadata {
      font-size: 11px;
      color: #72767d;
      margin-bottom: 3px;
    }

    .queue-info {
      color: #faa61a;
    }

    /* Enhanced Status Section */
    .enhanced-status {
      background: rgba(0, 0, 0, 0.2);
      padding: 8px 12px;
      border-top: 1px solid #40444b;
    }

    .status-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
    }

    .status-label {
      font-weight: 600;
      font-size: 12px;
    }

    .volume-info {
      color: #72767d;
      font-size: 11px;
    }

    .next-song {
      color: #b9bbbe;
      font-size: 11px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .controls {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
    }

    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 46px;
      height: 24px;
    }

    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .toggle-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #72767d;
      transition: .3s;
      border-radius: 24px;
    }

    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .3s;
      border-radius: 50%;
    }

    input:checked + .toggle-slider {
      background-color: #7289da;
    }

    input:checked + .toggle-slider:before {
      transform: translateX(22px);
    }

    .control-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
    }

    .control-label {
      font-weight: 500;
    }

    .button-container {
      display: flex;
      justify-content: space-between;
    }

    button {
      background-color: #4f545c;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 12px;
      font-size: 13px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    button:hover {
      background-color: #5d6269;
    }

    button.primary {
      background-color: #7289da;
    }

    button.primary:hover {
      background-color: #8296e0;
    }

    .footer {
      margin-top: 15px;
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #b9bbbe;
    }

    .footer a {
      color: #7289da;
      text-decoration: none;
    }

    .footer a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo" style="background-color: #ff0000; border-radius: 3px;"></div>
    <h1>YouTube Music Rich Presence</h1>
  </div>

  <div class="status-indicator">
    <div id="status-dot" class="status-dot disconnected"></div>
    <div id="status-text" class="status-text">Disconnected from Discord</div>
  </div>

  <div id="now-playing-container" class="now-playing empty">
    Not playing anything on YouTube Music
  </div>

  <div class="controls">
    <div class="control-item">
      <span class="control-label">Enable Rich Presence</span>
      <label class="toggle-switch">
        <input type="checkbox" id="enable-presence" checked>
        <span class="toggle-slider"></span>
      </label>
    </div>

    <div class="control-item">
      <span class="control-label">Private Mode</span>
      <label class="toggle-switch">
        <input type="checkbox" id="private-mode">
        <span class="toggle-slider"></span>
      </label>
    </div>
  </div>

  <div class="button-container">
    <button id="options-button">Open Settings</button>
    <button id="reconnect-button" class="primary">Reconnect</button>
  </div>

  <div class="footer">
    <span>v1.0.0</span>
    <a href="#" id="support-link">Need help?</a>
  </div>

  <script src="popup.js"></script>
</body>
</html>