{"meta": {"generator": "0.9.0", "format": 20, "date": 1600360080397}, "custom": {"general": {"name": "General", "files": {"welcome": {"name": "Welcome", "type": "md", "content": "<div align=\"center\">\n  <br />\n  <p>\n    <a href=\"https://discord.gg/bRCvFy9\"><img src=\"https://discordapp.com/api/guilds/222078108977594368/embed.png\" alt=\"Discord server\" /></a>\n    <a href=\"https://www.npmjs.com/package/discord-rpc\"><img src=\"https://img.shields.io/npm/v/discord-rpc.svg?maxAge=3600\" alt=\"NPM version\" /></a>\n    <a href=\"https://www.npmjs.com/package/discord-rpc\"><img src=\"https://img.shields.io/npm/dt/discord-rpc.svg?maxAge=3600\" alt=\"NPM downloads\" /></a>\n    <a href=\"https://david-dm.org/discordjs/RPC\"><img src=\"https://img.shields.io/david/discordjs/RPC.svg?maxAge=3600\" alt=\"Dependencies\" /></a>\n  </p>\n  <p>\n    <a href=\"https://nodei.co/npm/discord-rpc/\"><img src=\"https://nodei.co/npm/discord-rpc.png?downloads=true&stars=true\" alt=\"NPM info\" /></a>\n  </p>\n</div>\n\n# Discord.js RPC Extension\n\n### [Documentation](https://discord.js.org/#/docs/rpc/)\n\n### [Rich Presence Example](https://github.com/discordjs/RPC/blob/master/example)\n\n### __Browser__ Example\n\n```javascript\nconst clientId = '287406016902594560';\nconst scopes = ['rpc', 'rpc.api', 'messages.read'];\n\nconst client = new RPC.Client({ transport: 'websocket' });\n\nclient.on('ready', () => {\n  console.log('Logged in as', client.application.name);\n  console.log('Authed for user', client.user.username);\n\n  client.selectVoiceChannel('81384788862181376');\n});\n\n// Log in to RPC with client id\nclient.login({ clientId, scopes });\n```\n", "path": "README.md"}}}}, "classes": [{"name": "RPCClient", "description": "The main hub for interacting with Discord RPC", "extends": [[["BaseClient"]]], "construct": {"name": "RPCClient", "params": [{"name": "options", "description": "Options for the client.\nYou must provide a transport", "optional": true, "type": [[["RPCClientOptions"]]]}]}, "props": [{"name": "application", "description": "Application used in this client", "nullable": true, "type": [[["ClientApplication"]]], "meta": {"line": 41, "file": "client.js", "path": "src"}}, {"name": "user", "description": "User used in this application", "nullable": true, "type": [[["User"]]], "meta": {"line": 47, "file": "client.js", "path": "src"}}, {"name": "transport", "description": "Raw transport userd", "access": "private", "type": [[["RPCTransport"]]], "meta": {"line": 78, "file": "client.js", "path": "src"}}, {"name": "_expecting", "description": "Map of nonces being expected from the transport", "access": "private", "type": [[["Map"]]], "meta": {"line": 86, "file": "client.js", "path": "src"}}, {"name": "_subscriptions", "description": "Map of current subscriptions", "access": "private", "type": [[["Map"]]], "meta": {"line": 93, "file": "client.js", "path": "src"}}], "methods": [{"name": "connect", "description": "Search and connect to RPC", "meta": {"line": 101, "file": "client.js", "path": "src"}}, {"name": "login", "description": "Performs authentication flow. Automatically calls Client#connect if needed.", "examples": ["client.login({ clientId: '1234567', clientSecret: 'abcdef123' });"], "params": [{"name": "options", "description": "Options for authentication.\nAt least one property must be provided to perform login.", "type": [[["RPCLoginOptions"]]]}], "returns": [[["Promise", "<"], ["RPCClient", ">"]]], "meta": {"line": 142, "file": "client.js", "path": "src"}}, {"name": "request", "description": "Request", "access": "private", "params": [{"name": "cmd", "description": "Command", "type": [[["string"]]]}, {"name": "args", "description": "Arguments", "optional": true, "default": "{}", "type": [[["Object"]]]}, {"name": "evt", "description": "Event", "optional": true, "type": [[["string"]]]}], "returns": [[["Promise"]]], "meta": {"line": 163, "file": "client.js", "path": "src"}}, {"name": "_onRpcMessage", "description": "Message handler", "access": "private", "params": [{"name": "message", "description": "message", "type": [[["Object"]]]}], "meta": {"line": 176, "file": "client.js", "path": "src"}}, {"name": "authorize", "description": "Authorize", "access": "private", "params": [{"name": "options", "description": "options", "type": [[["Object"]]]}], "returns": [[["Promise"]]], "meta": {"line": 208, "file": "client.js", "path": "src"}}, {"name": "authenticate", "description": "Authenticate", "access": "private", "params": [{"name": "accessToken", "description": "access token", "type": [[["string"]]]}], "returns": [[["Promise"]]], "meta": {"line": 244, "file": "client.js", "path": "src"}}, {"name": "getGuild", "description": "Fetch a guild", "params": [{"name": "id", "description": "Guild ID", "type": [[["Snowflake"]]]}, {"name": "timeout", "description": "Timeout request", "optional": true, "type": [[["number"]]]}], "returns": [[["Promise", "<"], ["Guild", ">"]]], "meta": {"line": 262, "file": "client.js", "path": "src"}}, {"name": "getGuilds", "description": "Fetch all guilds", "params": [{"name": "timeout", "description": "Timeout request", "optional": true, "type": [[["number"]]]}], "returns": [[["Promise", "<"], ["Collection", "<"], ["Snowflake", ", "], ["Guild", ">>"]]], "meta": {"line": 271, "file": "client.js", "path": "src"}}, {"name": "getChannel", "description": "Get a channel", "params": [{"name": "id", "description": "Channel ID", "type": [[["Snowflake"]]]}, {"name": "timeout", "description": "Timeout request", "optional": true, "type": [[["number"]]]}], "returns": [[["Promise", "<"], ["Channel", ">"]]], "meta": {"line": 281, "file": "client.js", "path": "src"}}, {"name": "getChannels", "description": "Get all channels", "params": [{"name": "id", "description": "Guild ID", "optional": true, "type": [[["Snowflake"]]]}, {"name": "timeout", "description": "Timeout request", "optional": true, "type": [[["number"]]]}], "returns": [[["Promise", "<"], ["Collection", "<"], ["Snowflake", ", "], ["Channel", ">>"]]], "meta": {"line": 291, "file": "client.js", "path": "src"}}, {"name": "setCertifiedDevices", "description": "Tell discord which devices are certified", "params": [{"name": "devices", "description": "Certified devices to send to discord", "type": [[["Array", "<"], ["CertifiedDevice", ">"]]]}], "returns": [[["Promise"]]], "meta": {"line": 321, "file": "client.js", "path": "src"}}, {"name": "setUserVoiceSettings", "description": "Set the voice settings for a uer, by id", "params": [{"name": "id", "description": "ID of the user to set", "type": [[["Snowflake"]]]}, {"name": "settings", "description": "Settings", "type": [[["UserVoiceSettings"]]]}], "returns": [[["Promise"]]], "meta": {"line": 352, "file": "client.js", "path": "src"}}, {"name": "selectVoiceChannel", "description": "Move the user to a voice channel", "params": [{"name": "id", "description": "ID of the voice channel", "type": [[["Snowflake"]]]}, {"name": "options", "description": "Options", "optional": true, "type": [[["Object"]]]}, {"name": "options.timeout", "description": "Timeout for the command", "optional": true, "type": [[["number"]]]}, {"name": "options.force", "description": "Force this move. This should only be done if you\nhave explicit permission from the user.", "optional": true, "type": [[["boolean"]]]}], "returns": [[["Promise"]]], "meta": {"line": 370, "file": "client.js", "path": "src"}}, {"name": "selectTextChannel", "description": "Move the user to a text channel", "params": [{"name": "id", "description": "ID of the voice channel", "type": [[["Snowflake"]]]}, {"name": "options", "description": "Options", "optional": true, "type": [[["Object"]]]}, {"name": "options.timeout", "description": "Timeout for the command\nhave explicit permission from the user.", "optional": true, "type": [[["number"]]]}], "returns": [[["Promise"]]], "meta": {"line": 382, "file": "client.js", "path": "src"}}, {"name": "getVoiceSettings", "description": "Get current voice settings", "returns": [[["Promise"]]], "meta": {"line": 390, "file": "client.js", "path": "src"}}, {"name": "setVoiceSettings", "description": "Set current voice settings, overriding the current settings until this session disconnects.\nThis also locks the settings for any other rpc sessions which may be connected.", "params": [{"name": "args", "description": "Settings", "type": [[["Object"]]]}], "returns": [[["Promise"]]], "meta": {"line": 426, "file": "client.js", "path": "src"}}, {"name": "captureShortcut", "description": "Capture a shortcut using the client\nThe callback takes (key, stop) where `stop` is a function that will stop capturing.\nThis `stop` function must be called before disconnecting or else the user will have\nto restart their client.", "params": [{"name": "callback", "description": "Callback handling keys", "type": [[["function"]]]}], "returns": [[["Promise", "<"], ["function", "()>"]]], "meta": {"line": 461, "file": "client.js", "path": "src"}}, {"name": "setActivity", "description": "Sets the presence for the logged in user.", "params": [{"name": "args", "description": "The rich presence to pass.", "type": [[["object"]]]}, {"name": "pid", "description": "The application's process ID. Defaults to the executing process' PID.", "optional": true, "type": [[["number"]]]}], "returns": [[["Promise"]]], "meta": {"line": 480, "file": "client.js", "path": "src"}}, {"name": "clearActivity", "description": "Clears the currently set presence, if any. This will hide the \"Playing X\" message\ndisplayed below the user's name.", "params": [{"name": "pid", "description": "The application's process ID. Defaults to the executing process' PID.", "optional": true, "type": [[["number"]]]}], "returns": [[["Promise"]]], "meta": {"line": 548, "file": "client.js", "path": "src"}}, {"name": "sendJoinInvite", "description": "Invite a user to join the game the RPC user is currently playing", "params": [{"name": "user", "description": "The user to invite", "type": [[["User"]]]}], "returns": [[["Promise"]]], "meta": {"line": 559, "file": "client.js", "path": "src"}}, {"name": "sendJoinRequest", "description": "Request to join the game the user is playing", "params": [{"name": "user", "description": "The user whose game you want to request to join", "type": [[["User"]]]}], "returns": [[["Promise"]]], "meta": {"line": 570, "file": "client.js", "path": "src"}}, {"name": "closeJoinRequest", "description": "Reject a join request from a user", "params": [{"name": "user", "description": "The user whose request you wish to reject", "type": [[["User"]]]}], "returns": [[["Promise"]]], "meta": {"line": 581, "file": "client.js", "path": "src"}}, {"name": "subscribe", "description": "Subscribe to an event", "params": [{"name": "event", "description": "Name of event e.g. `MESSAGE_CREATE`", "type": [[["string"]]]}, {"name": "args", "description": "Args for event e.g. `{ channel_id: '1234' }`", "optional": true, "type": [[["Object"]]]}, {"name": "callback", "description": "Callback when an event for the subscription is triggered", "type": [[["function"]]]}], "returns": [[["Promise", "<"], ["Object", ">"]]], "meta": {"line": 655, "file": "client.js", "path": "src"}}, {"name": "destroy", "description": "Destroy the client", "meta": {"line": 673, "file": "client.js", "path": "src"}}], "meta": {"line": 29, "file": "client.js", "path": "src"}}], "interfaces": [], "typedefs": [], "externals": []}