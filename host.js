const DiscordRPC = require('discord-rpc');
const WebSocket = require('ws');
const fs = require('fs');
const path = require('path');

// Configuration
const clientId = '1375799244400623726';
const WS_PORT = 8765; // WebSocket port
const WS_HOST = 'localhost';

// State
let rpc = null;
let discordConnected = false;
let wsServer = null;
let connectedClients = new Set();

// Logging function
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;

  // Log to file
  try {
    const logPath = path.join(process.env.USERPROFILE || process.env.HOME, 'ytmusic-rpc-debug.log');
    fs.appendFileSync(logPath, logMessage);
  } catch (e) {
    // Ignore file write errors
  }

  // Log to console
  console.log(`[RPC-SERVER] ${message}`);
}

log('YouTube Music Discord RPC WebSocket Server starting...');

// Broadcast message to all connected clients
function broadcast(message) {
  const data = JSON.stringify(message);
  connectedClients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      try {
        client.send(data);
        log(`Sent to client: ${data}`);
      } catch (error) {
        log(`Error sending to client: ${error.message}`);
      }
    }
  });
}

// Discord connection
function connectToDiscord() {
  if (discordConnected) return Promise.resolve();

  log('Attempting to connect to Discord...');

  return new Promise((resolve, reject) => {
    // Create new RPC client
    rpc = new DiscordRPC.Client({ transport: 'ipc' });

    rpc.on('ready', () => {
      log('Successfully connected to Discord');
      discordConnected = true;
      broadcast({ event: 'discord_connected' });
      resolve();
    });

    rpc.on('disconnected', () => {
      log('Disconnected from Discord');
      discordConnected = false;
      broadcast({ event: 'discord_disconnected' });
    });

    // Try to login
    rpc.login({ clientId })
      .catch(error => {
        log(`Discord login failed: ${error.message}`);
        discordConnected = false;
        broadcast({ event: 'discord_connection_failed', error: error.message });
        reject(error);
      });
  });
}

// Handle activity updates
function setActivity(activity) {
  if (!connected) {
    log('Cannot set activity: not connected to Discord');
    return;
  }

  if (!activity) {
    log('Clearing Discord activity');
    rpc.clearActivity()
      .then(() => log('Activity cleared successfully'))
      .catch(error => log(`Error clearing activity: ${error.message}`));
    return;
  }

  log(`Setting Discord activity: ${JSON.stringify(activity)}`);
  rpc.setActivity(activity)
    .then(() => log('Activity set successfully'))
    .catch(error => log(`Error setting activity: ${error.message}`));
}

// Handle incoming messages
function handleMessage(message) {
  log(`Received message: ${JSON.stringify(message)}`);

  if (!message || !message.cmd) {
    log('Invalid message format');
    return;
  }

  switch (message.cmd) {
    case 'SET_ACTIVITY':
      if (message.args && message.args.activity !== undefined) {
        setActivity(message.args.activity);
      } else {
        log('Invalid SET_ACTIVITY message format');
      }
      break;
    default:
      log(`Unknown command: ${message.cmd}`);
  }
}

// Input handling
process.stdin.on('readable', () => {
  try {
    let chunk;
    while ((chunk = process.stdin.read()) !== null) {
      // Process the chunk
      const buffer = Buffer.from(chunk);

      if (buffer.length < 4) continue;

      const messageLength = buffer.readUInt32LE(0);
      if (buffer.length < messageLength + 4) continue;

      const messageData = buffer.slice(4, messageLength + 4);
      const message = JSON.parse(messageData.toString());

      handleMessage(message);
    }
  } catch (error) {
    log(`Error processing input: ${error.message}`);
  }
});

// Graceful shutdown
process.on('SIGINT', () => {
  log('Received SIGINT, shutting down...');
  if (connected) {
    rpc.destroy();
  }
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('Received SIGTERM, shutting down...');
  if (connected) {
    rpc.destroy();
  }
  process.exit(0);
});

// Start connection
log('Starting Discord connection...');
connectToDiscord()
  .then(() => {
    log('Host initialized successfully');
  })
  .catch(error => {
    log(`Failed to initialize: ${error.message}`);
  });

log('Host setup complete, waiting for messages...')