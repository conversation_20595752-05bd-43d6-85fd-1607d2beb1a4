const DiscordRPC = require('discord-rpc');
const fs = require('fs');
const path = require('path');

// Discord application client ID
const clientId = '1375799244400623726';

// Logging function
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  
  // Log to file
  try {
    const logPath = path.join(process.env.USERPROFILE || process.env.HOME, 'ytmusic-rpc-debug.log');
    fs.appendFileSync(logPath, logMessage);
  } catch (e) {
    // Ignore file write errors
  }
  
  // Log to stderr (visible in extension console)
  console.error(`[HOST] ${message}`);
}

log('YouTube Music Discord RPC Host starting...');

// Create RPC client
const rpc = new DiscordRPC.Client({ transport: 'ipc' });
let connected = false;
let reconnectTimeout = null;

// Native messaging protocol
function sendMessage(message) {
  try {
    const json = JSON.stringify(message);
    const buffer = Buffer.from(json);
    const header = Buffer.alloc(4);
    header.writeUInt32LE(buffer.length, 0);
    
    process.stdout.write(Buffer.concat([header, buffer]));
    log(`Sent message: ${json}`);
  } catch (error) {
    log(`Error sending message: ${error.message}`);
  }
}

// Discord connection
function connectToDiscord() {
  if (connected) return Promise.resolve();
  
  log('Attempting to connect to Discord...');
  
  return new Promise((resolve, reject) => {
    // Clear any existing timeout
    if (reconnectTimeout) {
      clearTimeout(reconnectTimeout);
      reconnectTimeout = null;
    }
    
    rpc.on('ready', () => {
      log('Successfully connected to Discord');
      connected = true;
      sendMessage({ event: 'connected' });
      resolve();
    });
    
    rpc.on('disconnected', () => {
      log('Disconnected from Discord');
      connected = false;
      
      // Auto-reconnect after 5 seconds
      reconnectTimeout = setTimeout(() => {
        connectToDiscord().catch(error => {
          log(`Reconnection failed: ${error.message}`);
        });
      }, 5000);
    });
    
    // Try to login
    rpc.login({ clientId })
      .catch(error => {
        log(`Discord login failed: ${error.message}`);
        connected = false;
        reject(error);
        
        // Retry after 10 seconds
        reconnectTimeout = setTimeout(() => {
          connectToDiscord().catch(err => {
            log(`Retry connection failed: ${err.message}`);
          });
        }, 10000);
      });
  });
}

// Handle activity updates
function setActivity(activity) {
  if (!connected) {
    log('Cannot set activity: not connected to Discord');
    return;
  }
  
  if (!activity) {
    log('Clearing Discord activity');
    rpc.clearActivity()
      .then(() => log('Activity cleared successfully'))
      .catch(error => log(`Error clearing activity: ${error.message}`));
    return;
  }
  
  log(`Setting Discord activity: ${JSON.stringify(activity)}`);
  rpc.setActivity(activity)
    .then(() => log('Activity set successfully'))
    .catch(error => log(`Error setting activity: ${error.message}`));
}

// Handle incoming messages
function handleMessage(message) {
  log(`Received message: ${JSON.stringify(message)}`);
  
  if (!message || !message.cmd) {
    log('Invalid message format');
    return;
  }
  
  switch (message.cmd) {
    case 'SET_ACTIVITY':
      if (message.args && message.args.activity !== undefined) {
        setActivity(message.args.activity);
      } else {
        log('Invalid SET_ACTIVITY message format');
      }
      break;
    default:
      log(`Unknown command: ${message.cmd}`);
  }
}

// Input handling
process.stdin.on('readable', () => {
  try {
    let chunk;
    while ((chunk = process.stdin.read()) !== null) {
      // Process the chunk
      const buffer = Buffer.from(chunk);
      
      if (buffer.length < 4) continue;
      
      const messageLength = buffer.readUInt32LE(0);
      if (buffer.length < messageLength + 4) continue;
      
      const messageData = buffer.slice(4, messageLength + 4);
      const message = JSON.parse(messageData.toString());
      
      handleMessage(message);
    }
  } catch (error) {
    log(`Error processing input: ${error.message}`);
  }
});

// Graceful shutdown
process.on('SIGINT', () => {
  log('Received SIGINT, shutting down...');
  if (connected) {
    rpc.destroy();
  }
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('Received SIGTERM, shutting down...');
  if (connected) {
    rpc.destroy();
  }
  process.exit(0);
});

// Start connection
log('Starting Discord connection...');
connectToDiscord()
  .then(() => {
    log('Host initialized successfully');
  })
  .catch(error => {
    log(`Failed to initialize: ${error.message}`);
  });

log('Host setup complete, waiting for messages...');
