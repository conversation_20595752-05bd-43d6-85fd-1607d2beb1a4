// Imgur Upload Service for Discord Rich Presence Thumbnails
// This uploads YouTube thumbnails to Imgur for Discord compatibility

const https = require('https');
const fs = require('fs');
const path = require('path');

// Imgur API configuration
const IMGUR_CLIENT_ID = 'YOUR_IMGUR_CLIENT_ID'; // Get from https://api.imgur.com/oauth2/addclient
const IMGUR_API_URL = 'https://api.imgur.com/3/image';

class ImgurUploader {
  constructor() {
    this.cache = new Map(); // URL -> Imgur URL cache
    this.cacheFile = path.join(__dirname, 'imgur_cache.json');
    this.loadCache();
  }

  // Load cache from file
  loadCache() {
    try {
      if (fs.existsSync(this.cacheFile)) {
        const data = fs.readFileSync(this.cacheFile, 'utf8');
        const cacheData = JSON.parse(data);
        this.cache = new Map(Object.entries(cacheData));
        console.log(`Loaded ${this.cache.size} cached Imgur URLs`);
      }
    } catch (error) {
      console.error('Error loading cache:', error);
    }
  }

  // Save cache to file
  saveCache() {
    try {
      const cacheData = Object.fromEntries(this.cache);
      fs.writeFileSync(this.cacheFile, JSON.stringify(cacheData, null, 2));
    } catch (error) {
      console.error('Error saving cache:', error);
    }
  }

  // Download image from URL
  async downloadImage(url) {
    return new Promise((resolve, reject) => {
      https.get(url, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}`));
          return;
        }

        const chunks = [];
        response.on('data', (chunk) => chunks.push(chunk));
        response.on('end', () => resolve(Buffer.concat(chunks)));
        response.on('error', reject);
      }).on('error', reject);
    });
  }

  // Upload image to Imgur
  async uploadToImgur(imageBuffer) {
    return new Promise((resolve, reject) => {
      const postData = JSON.stringify({
        image: imageBuffer.toString('base64'),
        type: 'base64'
      });

      const options = {
        hostname: 'api.imgur.com',
        port: 443,
        path: '/3/image',
        method: 'POST',
        headers: {
          'Authorization': `Client-ID ${IMGUR_CLIENT_ID}`,
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        }
      };

      const req = https.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            if (response.success) {
              resolve(response.data.link);
            } else {
              reject(new Error(response.data.error || 'Upload failed'));
            }
          } catch (error) {
            reject(error);
          }
        });
      });

      req.on('error', reject);
      req.write(postData);
      req.end();
    });
  }

  // Convert YouTube thumbnail to Imgur URL
  async convertThumbnail(youtubeUrl) {
    try {
      // Check cache first
      if (this.cache.has(youtubeUrl)) {
        console.log('Using cached Imgur URL');
        return this.cache.get(youtubeUrl);
      }

      console.log('Downloading thumbnail:', youtubeUrl);
      
      // Download the image
      const imageBuffer = await this.downloadImage(youtubeUrl);
      
      // Upload to Imgur
      console.log('Uploading to Imgur...');
      const imgurUrl = await this.uploadToImgur(imageBuffer);
      
      // Cache the result
      this.cache.set(youtubeUrl, imgurUrl);
      this.saveCache();
      
      console.log('Thumbnail uploaded:', imgurUrl);
      return imgurUrl;
      
    } catch (error) {
      console.error('Error converting thumbnail:', error);
      return null;
    }
  }

  // Get cached URL without uploading
  getCachedUrl(youtubeUrl) {
    return this.cache.get(youtubeUrl) || null;
  }

  // Clear cache
  clearCache() {
    this.cache.clear();
    if (fs.existsSync(this.cacheFile)) {
      fs.unlinkSync(this.cacheFile);
    }
  }
}

// Export for use in other modules
module.exports = ImgurUploader;

// CLI usage
if (require.main === module) {
  const uploader = new ImgurUploader();
  
  console.log('🖼️  Imgur Thumbnail Uploader');
  console.log('=============================');
  console.log('');
  
  if (IMGUR_CLIENT_ID === 'YOUR_IMGUR_CLIENT_ID') {
    console.log('❌ Please set your Imgur Client ID first!');
    console.log('');
    console.log('1. Go to: https://api.imgur.com/oauth2/addclient');
    console.log('2. Create a new application');
    console.log('3. Copy the Client ID');
    console.log('4. Replace YOUR_IMGUR_CLIENT_ID in this file');
    console.log('');
    process.exit(1);
  }
  
  // Test with a sample URL
  const testUrl = 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg';
  
  console.log('Testing with sample YouTube thumbnail...');
  uploader.convertThumbnail(testUrl)
    .then(imgurUrl => {
      if (imgurUrl) {
        console.log('✅ Success! Imgur URL:', imgurUrl);
        console.log('');
        console.log('This URL can be used in Discord Rich Presence!');
      } else {
        console.log('❌ Failed to upload thumbnail');
      }
    })
    .catch(error => {
      console.error('❌ Error:', error.message);
    });
}
