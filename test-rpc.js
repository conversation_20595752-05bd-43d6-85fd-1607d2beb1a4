// Test script to verify Discord RPC with proper assets
const DiscordRPC = require('discord-rpc');

const clientId = '1375799244400623726';
const rpc = new DiscordRPC.Client({ transport: 'ipc' });

rpc.on('ready', () => {
  console.log('✅ Connected to Discord!');
  console.log('');

  // Test different configurations including real thumbnails
  const tests = [
    {
      name: 'Test 1: No Assets (Default Discord Icon)',
      activity: {
        details: '🎵 Test Song - No Assets',
        state: '👤 Test Artist',
        timestamps: {
          start: Date.now(),
          end: Date.now() + 180000
        },
        buttons: [
          {
            label: '🎵 Listen on YouTube Music',
            url: 'https://music.youtube.com'
          }
        ]
      }
    },
    {
      name: 'Test 2: With Real Song Thumbnail (Proxied)',
      activity: {
        details: '🎵 Test Song - Real Thumbnail',
        state: '👤 Test Artist',
        assets: {
          large_image: 'https://images.weserv.nl/?url=https%3A//i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg&w=512&h=512&fit=cover',
          large_text: 'Album Art via Proxy'
        },
        timestamps: {
          start: Date.now(),
          end: Date.now() + 180000
        },
        buttons: [
          {
            label: '🎵 Listen on YouTube Music',
            url: 'https://music.youtube.com'
          }
        ]
      }
    },
    {
      name: 'Test 3: Different Song Thumbnail',
      activity: {
        details: '🎵 Another Test Song',
        state: '👤 Another Artist',
        assets: {
          large_image: 'https://images.weserv.nl/?url=https%3A//i.ytimg.com/vi/9bZkp7q19f0/maxresdefault.jpg&w=512&h=512&fit=cover',
          large_text: 'Different Album Art'
        },
        timestamps: {
          start: Date.now(),
          end: Date.now() + 180000
        }
      }
    }
  ];

  let currentTest = 0;

  function runNextTest() {
    if (currentTest >= tests.length) {
      console.log('');
      console.log('🎯 All tests completed!');
      console.log('');
      console.log('Results:');
      console.log('- If Test 1 shows an image: ytmusic_logo asset exists ✅');
      console.log('- If Test 2 shows an image: music_note asset exists ✅');
      console.log('- If Test 3 shows an image: External URLs work (rare) ✅');
      console.log('- If no images show: Assets need to be uploaded to Discord ❌');
      console.log('');
      console.log('To upload assets:');
      console.log('1. Run: node setup-discord-assets.js');
      console.log('2. Follow the instructions to upload assets');
      console.log('');
      console.log('Press Ctrl+C to exit.');
      return;
    }

    const test = tests[currentTest];
    console.log(`🧪 Running ${test.name}...`);

    rpc.setActivity(test.activity)
      .then(() => {
        console.log(`✅ ${test.name} set successfully!`);
        console.log('Check Discord, then press Enter for next test...');

        // Wait for user input
        process.stdin.once('data', () => {
          currentTest++;
          runNextTest();
        });
      })
      .catch(error => {
        console.error(`❌ ${test.name} failed:`, error.message);
        currentTest++;
        setTimeout(runNextTest, 2000);
      });
  }

  // Start testing
  console.log('🧪 Starting Discord RPC Asset Tests...');
  console.log('');
  console.log('This will test different image configurations to see what works.');
  console.log('Press Enter to start...');

  process.stdin.once('data', () => {
    runNextTest();
  });
});

rpc.on('disconnected', () => {
  console.log('❌ Disconnected from Discord');
});

rpc.login({ clientId })
  .catch(error => {
    console.error('❌ Failed to connect to Discord:', error);
    console.log('Make sure Discord is running and try again.');
  });

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down...');
  if (rpc) {
    rpc.destroy();
  }
  process.exit(0);
});
