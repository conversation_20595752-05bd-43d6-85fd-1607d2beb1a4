// Simple test script to verify Discord RPC with thumbnails
const DiscordRPC = require('discord-rpc');

const clientId = '1375799244400623726';
const rpc = new DiscordRPC.Client({ transport: 'ipc' });

rpc.on('ready', () => {
  console.log('✅ Connected to Discord!');
  
  // Test with a sample YouTube Music thumbnail
  const testActivity = {
    details: '🎵 Test Song Title',
    state: '👤 Test Artist',
    assets: {
      large_image: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg', // Sample YouTube thumbnail
      large_text: 'Test Album',
      small_image: 'ytmusic_logo',
      small_text: '▶️ Playing'
    },
    timestamps: {
      start: Date.now(),
      end: Date.now() + 180000 // 3 minutes from now
    },
    buttons: [
      {
        label: '🎵 Listen on YouTube Music',
        url: 'https://music.youtube.com'
      }
    ]
  };

  console.log('Setting test activity:', testActivity);
  
  rpc.setActivity(testActivity)
    .then(() => {
      console.log('✅ Activity set successfully!');
      console.log('Check Discord to see if the thumbnail appears.');
      console.log('Press Ctrl+C to exit.');
    })
    .catch(error => {
      console.error('❌ Error setting activity:', error);
    });
});

rpc.on('disconnected', () => {
  console.log('❌ Disconnected from Discord');
});

rpc.login({ clientId })
  .catch(error => {
    console.error('❌ Failed to connect to Discord:', error);
    console.log('Make sure Discord is running and try again.');
  });

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down...');
  if (rpc) {
    rpc.destroy();
  }
  process.exit(0);
});
