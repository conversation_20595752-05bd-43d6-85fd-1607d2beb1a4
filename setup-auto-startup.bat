@echo off
title YouTube Music Discord RPC - Setup Auto-Startup
color 0B
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║       YouTube Music Discord RPC - Auto-Startup Setup        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo This will set up the Discord RPC server to start automatically
echo when you open YouTube Music.
echo.

:: Get current directory
set "CURRENT_DIR=%~dp0"
set "STARTUP_FOLDER=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup"
set "SHORTCUT_NAME=YouTube Music Discord RPC.lnk"

echo Choose your setup option:
echo.
echo [1] Add to Windows Startup (starts with Windows)
echo [2] Create Desktop Shortcut (manual start)
echo [3] Both options
echo [4] Remove from startup
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto :startup_only
if "%choice%"=="2" goto :desktop_only
if "%choice%"=="3" goto :both
if "%choice%"=="4" goto :remove
goto :invalid

:startup_only
echo.
echo 📁 Adding to Windows Startup folder...
call :create_startup_shortcut
echo ✅ Added to startup! The server will start when Windows starts.
goto :end

:desktop_only
echo.
echo 🖥️ Creating desktop shortcut...
call :create_desktop_shortcut
echo ✅ Desktop shortcut created!
goto :end

:both
echo.
echo 📁 Adding to Windows Startup folder...
call :create_startup_shortcut
echo 🖥️ Creating desktop shortcut...
call :create_desktop_shortcut
echo ✅ Both shortcuts created!
goto :end

:remove
echo.
echo 🗑️ Removing from startup...
if exist "%STARTUP_FOLDER%\%SHORTCUT_NAME%" (
    del "%STARTUP_FOLDER%\%SHORTCUT_NAME%"
    echo ✅ Removed from startup folder
) else (
    echo ⚠️ No startup shortcut found
)
goto :end

:create_startup_shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTUP_FOLDER%\%SHORTCUT_NAME%'); $Shortcut.TargetPath = '%CURRENT_DIR%auto-start-server.bat'; $Shortcut.WorkingDirectory = '%CURRENT_DIR%'; $Shortcut.Description = 'YouTube Music Discord RPC Server'; $Shortcut.Save()"
goto :eof

:create_desktop_shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\YouTube Music Discord RPC.lnk'); $Shortcut.TargetPath = '%CURRENT_DIR%auto-start-server.bat'; $Shortcut.WorkingDirectory = '%CURRENT_DIR%'; $Shortcut.Description = 'YouTube Music Discord RPC Server'; $Shortcut.Save()"
goto :eof

:invalid
echo ❌ Invalid choice. Please run the script again.
goto :end

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        Setup Complete!                      ║
echo ║                                                              ║
echo ║  Now when you open YouTube Music, the extension will        ║
echo ║  automatically detect and connect to the Discord RPC!       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
pause
