const fs = require('fs');
const DiscordRPC = require('discord-rpc');

// Replace with your Discord application client ID
const clientId = '1375799244400623726';

// Set up RPC
const rpc = new DiscordRPC.Client({ transport: 'ipc' });
let connected = false;

// Handle browser messages
process.stdin.on('readable', () => {
  let input = [];
  let chunk;
  while ((chunk = process.stdin.read()) !== null) {
    input.push(chunk);
  }
  
  if (input.length > 0) {
    const buffer = Buffer.concat(input);
    const msgLen = buffer.readUInt32LE(0);
    const dataBuffer = buffer.slice(4, msgLen + 4);
    
    try {
      const message = JSON.parse(dataBuffer.toString());
      handleMessage(message);
    } catch (error) {
      console.error('Error parsing message:', error);
    }
  }
});

// Handle incoming messages
function handleMessage(message) {
  if (!message || !message.cmd) return;
  
  switch (message.cmd) {
    case 'SET_ACTIVITY':
      if (!connected) {
        connectToDiscord()
          .then(() => setActivity(message.args.activity))
          .catch(console.error);
      } else {
        setActivity(message.args.activity);
      }
      break;
  }
}

// Connect to Discord
function connectToDiscord() {
  if (connected) return Promise.resolve();
  
  return new Promise((resolve, reject) => {
    rpc.on('ready', () => {
      connected = true;
      sendMessage({ event: 'connected' });
      resolve();
    });
    
    rpc.login({ clientId }).catch(err => {
      console.error('Error connecting to Discord:', err);
      reject(err);
    });
  });
}

// Set Discord activity
function setActivity(activity) {
  if (!connected || !activity) return;
  
  rpc.setActivity(activity)
    .catch(console.error);
}

// Send message back to browser
function sendMessage(message) {
  const json = JSON.stringify(message);
  const buffer = Buffer.from(json);
  
  const header = Buffer.alloc(4);
  header.writeUInt32LE(buffer.length, 0);
  
  process.stdout.write(Buffer.concat([header, buffer]));
}

// Handle process exit
process.on('SIGINT', () => {
  rpc.destroy();
  process.exit();
});
