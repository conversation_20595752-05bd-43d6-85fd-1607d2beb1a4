const fs = require('fs');
const DiscordRPC = require('discord-rpc');
const path = require('path');

// Add this at the beginning of your host.js script
console.error = function() {
  fs.appendFileSync(
    path.join(process.env.USERPROFILE || process.env.HOME, 'ytmusic-rpc-log.txt'),
    Array.from(arguments).join(' ') + '\\n'
  );
};
console.log = console.error;

// Replace with your Discord application client ID
const clientId = '1375799244400623726';

// Find Vencord IPC path
function findVencordIPCPath() {
  // Common Vencord IPC paths based on operating system
  let possiblePaths = [];
  
  if (process.platform === 'win32') {
    // Windows paths - Vencord typically uses a pipe named 'discord-ipc-0'
    // Check both standard location and Vencord specific ones
    possiblePaths = [
      '\\\\?\\pipe\\discord-ipc-0',
      '\\\\?\\pipe\\vencord-ipc-0'
    ];
  } else {
    // Unix-like systems (Linux/macOS)
    const tempDir = process.env.XDG_RUNTIME_DIR || process.env.TMPDIR || process.env.TMP || '/tmp';
    possiblePaths = [
      path.join(tempDir, 'discord-ipc-0'),
      path.join(tempDir, 'vencord-ipc-0'),
      path.join(process.env.HOME, '.vencord', 'discord-ipc-0')
    ];
  }
  
  // Find the first existing path
  for (const socketPath of possiblePaths) {
    try {
      if (process.platform === 'win32' || fs.existsSync(socketPath)) {
        console.log(`Using IPC path: ${socketPath}`);
        return socketPath;
      }
    } catch (e) {
      // Ignore errors, just try the next path
    }
  }
  
  // Default to standard Discord IPC if nothing else is found
  return '';
}

// Set up RPC with Vencord's IPC socket
const rpc = new DiscordRPC.Client({ 
  transport: 'ipc',
  clientId: clientId,
  socketPath: findVencordIPCPath()
});
let connected = false;

// Handle browser messages
process.stdin.on('readable', () => {
  let input = [];
  let chunk;
  while ((chunk = process.stdin.read()) !== null) {
    input.push(chunk);
  }
  
  if (input.length > 0) {
    const buffer = Buffer.concat(input);
    const msgLen = buffer.readUInt32LE(0);
    const dataBuffer = buffer.slice(4, msgLen + 4);
    
    try {
      const message = JSON.parse(dataBuffer.toString());
      handleMessage(message);
    } catch (error) {
      console.error('Error parsing message:', error);
    }
  }
});

// Handle incoming messages
function handleMessage(message) {
  if (!message || !message.cmd) return;
  
  switch (message.cmd) {
    case 'SET_ACTIVITY':
      if (!connected) {
        connectToDiscord()
          .then(() => setActivity(message.args.activity))
          .catch(console.error);
      } else {
        setActivity(message.args.activity);
      }
      break;
  }
}

// Connect to Discord/Vencord
function connectToDiscord() {
  if (connected) return Promise.resolve();
  
  return new Promise((resolve, reject) => {
    rpc.on('ready', () => {
      connected = true;
      sendMessage({ event: 'connected' });
      resolve();
    });
    
    rpc.login({ clientId }).catch(err => {
      console.error('Error connecting to Discord/Vencord:', err);
      reject(err);
    });
  });
}

// Set Discord activity
function setActivity(activity) {
  if (!connected || !activity) return;
  
  rpc.setActivity(activity)
    .catch(console.error);
}

// Send message back to browser
function sendMessage(message) {
  const json = JSON.stringify(message);
  const buffer = Buffer.from(json);
  
  const header = Buffer.alloc(4);
  header.writeUInt32LE(buffer.length, 0);
  
  process.stdout.write(Buffer.concat([header, buffer]));
}

// Handle process exit
process.on('SIGINT', () => {
  rpc.destroy();
  process.exit();
});
