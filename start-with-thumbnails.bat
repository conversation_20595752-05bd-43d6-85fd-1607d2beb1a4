@echo off
echo ========================================
echo  YouTube Music Discord RPC + Thumbnails
echo ========================================
echo.
echo Checking dependencies...

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install discord-rpc ws express
    if errorlevel 1 (
        echo.
        echo ❌ Failed to install dependencies!
        echo Make sure Node.js and npm are installed.
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully!
    echo.
)

echo 🖼️  Starting YouTube Music Discord RPC with REAL THUMBNAILS!
echo.
echo ✨ Features:
echo - 🖼️ REAL song thumbnails from YouTube Music
echo - ⏱️ Real-time progress bar (like Spotify)
echo - 🎵 Enhanced formatting with emojis
echo - 🔗 Two clickable buttons
echo - 🚀 Modern visual effects
echo - 💾 Local thumbnail caching for speed
echo.
echo 🔧 Starting services...
echo.

REM Start thumbnail service in background
echo 📥 Starting thumbnail proxy service...
start /B "Thumbnail Service" node thumbnail-service.js

REM Wait a moment for thumbnail service to start
timeout /t 3 /nobreak >nul

REM Start main WebSocket server
echo 🚀 Starting Discord RPC server...
start /B "Discord RPC" node websocket-host.js

REM Wait a moment for services to start
timeout /t 3 /nobreak >nul

echo.
echo ✅ Services started successfully!
echo.
echo 🌐 Thumbnail Service: http://localhost:3000
echo 🔌 Discord RPC Server: localhost:8765
echo.
echo 🎯 Next Steps:
echo 1. Reload your Chrome extension
echo 2. Open YouTube Music and play a song
echo 3. Check Discord - you should see REAL song thumbnails!
echo.
echo 💡 How it works:
echo - Thumbnail service downloads YouTube thumbnails
echo - Converts them to Discord-compatible URLs
echo - Caches them locally for fast loading
echo - Your Discord shows actual song artwork!
echo.
echo Press any key to open service status pages...
pause >nul

REM Open status pages
start http://localhost:3000/health
start http://localhost:8765

echo.
echo 🎵 Ready! Play music on YouTube Music to see thumbnails in Discord!
echo.
echo Press any key to stop all services...
pause >nul

REM Stop services
echo.
echo 🛑 Stopping services...
taskkill /F /FI "WINDOWTITLE eq Thumbnail Service*" >nul 2>&1
taskkill /F /FI "WINDOWTITLE eq Discord RPC*" >nul 2>&1
echo ✅ Services stopped.
pause
