// Discord Rich Presence for YouTube Music - Enhanced Content Script

// Configuration
const UPDATE_INTERVAL = 5000; // Check for new song every 5 seconds
let currentSong = null;
let currentState = null;
let intervalId = null;
let lastUpdate = null;

// Start the extension
function initialize() {
  console.log("YouTube Music Discord Rich Presence: Content script initialized");

  // Setup mutation observer to detect page changes
  setupMutationObserver();

  // Start the interval for regular checks
  startTrackingInterval();
}

// Set up mutation observer to detect DOM changes
function setupMutationObserver() {
  const observer = new MutationObserver((mutations) => {
    // Check if there were significant changes that might indicate song change
    const relevantChanges = mutations.some(mutation => {
      return mutation.target.classList && (
        mutation.target.classList.contains('content-info-wrapper') || 
        mutation.target.classList.contains('ytmusic-player-bar')
      );
    });

    // If relevant changes detected, check for song update
    if (relevantChanges) {
      checkForSongUpdate();
    }
  });

  // Start observing the document body for changes
  observer.observe(document.body, { 
    subtree: true, 
    attributes: true, 
    childList: true,
    attributeFilter: ['src', 'class', 'style'] 
  });
}

// Start interval for checking song updates
function startTrackingInterval() {
  if (intervalId) {
    clearInterval(intervalId);
  }

  // Check immediately
  checkForSongUpdate();

  // Then check periodically
  intervalId = setInterval(checkForSongUpdate, UPDATE_INTERVAL);
}

// Check if the current song has changed
function checkForSongUpdate() {
  const songInfo = getSongInfo();

  // If no song info found, return
  if (!songInfo) {
    return;
  }

  // Check if song has changed or playback state changed
  const songChanged = currentSong !== songInfo.title;
  const stateChanged = currentState !== songInfo.isPlaying;

  // If changed, update the stored info and send update
  if (songChanged || stateChanged || shouldUpdateTimestamp(songInfo)) {
    currentSong = songInfo.title;
    currentState = songInfo.isPlaying;
    lastUpdate = Date.now();

    // Send update to background script
    chrome.runtime.sendMessage({
      type: 'song_update',
      data: {
        ...songInfo,
        reportedAt: Date.now() // Add timestamp when data was reported
      }
    });
  }
}

// Determine if we should update just for timestamp changes
function shouldUpdateTimestamp(songInfo) {
  // Update at least every 30 seconds while playing
  if (songInfo.isPlaying && lastUpdate && (Date.now() - lastUpdate > 30000)) {
    return true;
  }
  return false;
}

// Extract song information from the page
function getSongInfo() {
  try {
    // Check if there's a video playing
    const videoElement = document.querySelector('video');
    if (!videoElement) {
      return null;
    }

    // Get song title
    const titleElement = document.querySelector('.title.ytmusic-player-bar');
    const title = titleElement ? titleElement.textContent.trim() : 'Unknown Track';

    // Get artist
    const artistElement = document.querySelector('.byline.ytmusic-player-bar');
    const artist = artistElement ? artistElement.textContent.replace(' â€¢ ', '').trim() : 'Unknown Artist';

    // If the title is empty, assume no song is playing
    if (!title || title === '') {
      return null;
    }

    // Get album - this might be more difficult, parse from subtitle if available
    let album = 'Unknown Album';
    try {
      const subtitleElements = document.querySelectorAll('.subtitle.ytmusic-player-bar .subtitle-string');
      if (subtitleElements && subtitleElements.length > 1) {
        album = subtitleElements[1].textContent.trim();
      }
    } catch (e) {
      console.warn("Error extracting album name", e);
    }

    // Get play state
    const isPlaying = !videoElement.paused;

    // Get current time and duration
    const currentTime = videoElement.currentTime;
    const duration = videoElement.duration;

    // Get thumbnail with enhanced resolution
    const thumbnailElement = document.querySelector('img.ytmusic-player-bar');
    let thumbnail = null;
    if (thumbnailElement && thumbnailElement.src) {
      // YouTube Music thumbnails often end with =w60-h60
      // Try to get a higher resolution version
      let thumbnailUrl = thumbnailElement.src;

      // Replace dimensions for higher quality
      if (thumbnailUrl.includes('=w')) {
        // Match resolution pattern
        thumbnailUrl = thumbnailUrl.replace(/=w\d+-h\d+/, '=w320-h320');
      }
      thumbnail = thumbnailUrl;
    }

    return {
      title,
      artist,
      album,
      thumbnail,
      isPlaying,
      currentTime,
      duration
    };
  } catch (e) {
    console.error("Error getting song info:", e);
    return null;
  }
}

// Initialize the extension when the page is loaded
if (document.readyState === 'complete') {
  initialize();
} else {
  window.addEventListener('load', initialize);
}