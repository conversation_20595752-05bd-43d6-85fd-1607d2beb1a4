// Discord Rich Presence for YouTube Music - Content Script

// Configuration
const UPDATE_INTERVAL = 5000; // Check for new song every 5 seconds
let currentSong = null;
let currentState = null;
let intervalId = null;
let lastUpdate = null;

// Start the extension
function initialize() {
  console.log("YouTube Music Discord Rich Presence: Content script initialized");
  
  // Setup mutation observer to detect page changes
  setupMutationObserver();
  
  // Start the interval for regular checks
  startTrackingInterval();
}

// Set up mutation observer to detect DOM changes
function setupMutationObserver() {
  const observer = new MutationObserver((mutations) => {
    // Check if there were significant changes that might indicate song change
    const relevantChanges = mutations.some(mutation => {
      return mutation.target.classList && 
        (mutation.target.classList.contains('content-info-wrapper') || 
         mutation.target.classList.contains('ytmusic-player-bar'));
    });
    
    if (relevantChanges) {
      checkForSongUpdate();
    }
  });
  
  // Observe the player bar for changes
  const playerBar = document.querySelector('ytmusic-player-bar');
  if (playerBar) {
    observer.observe(playerBar, { 
      childList: true, 
      subtree: true, 
      attributes: true,
      attributeFilter: ['class', 'src', 'style']
    });
  }
}

// Start tracking interval
function startTrackingInterval() {
  if (intervalId) clearInterval(intervalId);
  
  // Initial check
  checkForSongUpdate();
  
  // Set up interval for regular checks
  intervalId = setInterval(checkForSongUpdate, UPDATE_INTERVAL);
}

// Check if song information has changed
function checkForSongUpdate() {
  try {
    const songInfo = extractSongInfo();
    const playbackState = getPlaybackState();
    
    // If song or playback state changed, update Discord presence
    if (shouldUpdatePresence(songInfo, playbackState)) {
      lastUpdate = Date.now();
      currentSong = songInfo;
      currentState = playbackState;
      
      if (playbackState === 'playing') {
        const activityData = createActivityData(songInfo);
        chrome.runtime.sendMessage({
          type: "updateActivity",
          data: activityData
        });
        console.log("Updated Discord presence with new song:", songInfo.title);
      } else {
        chrome.runtime.sendMessage({
          type: "clearActivity"
        });
        console.log("Cleared Discord presence - music paused");
      }
    }
  } catch (error) {
    console.error("Error updating song info:", error);
  }
}

// Determine if we should send a presence update
function shouldUpdatePresence(songInfo, playbackState) {
  // Send update if any of these conditions are true:
  
  // 1. First time checking or previous check failed
  if (!currentSong || !currentState) return true;
  
  // 2. Playback state changed (playing/paused)
  if (currentState !== playbackState) return true;
  
  // 3. Song changed
  if (currentSong.title !== songInfo.title || 
      currentSong.artist !== songInfo.artist) return true;
  
  // 4. Significant time passed since last update (3 minutes)
  if (Date.now() - lastUpdate > 180000) return true;
  
  return false;
}

// Extract song information from the YouTube Music player
function extractSongInfo() {
  const songInfo = {
    title: "",
    artist: "",
    album: "",
    albumArt: "",
    songUrl: window.location.href,
    duration: 0,
    position: 0
  };

  try {
    // Get song title
    const titleElement = document.querySelector('.ytmusic-player-bar.title');
    if (titleElement) {
      songInfo.title = titleElement.textContent.trim();
    }
    
    // Get artist name
    const artistElement = document.querySelector('.ytmusic-player-bar .byline a');
    if (artistElement) {
      songInfo.artist = artistElement.textContent.trim();
    }
    
    // Get album name (if available)
    const subtitleElements = document.querySelectorAll('.ytmusic-player-bar .byline');
    if (subtitleElements && subtitleElements.length > 0) {
      // Try to find the album part in the byline
      const bylineText = subtitleElements[0].textContent;
      const albumMatch = bylineText.match(/• (.+?)( •|$)/);
      if (albumMatch && albumMatch[1]) {
        songInfo.album = albumMatch[1].trim();
      }
    }
    
    // Get album art
    const albumArtElement = document.querySelector('.ytmusic-player-bar img.image');
    if (albumArtElement && albumArtElement.src) {
      // Get the highest resolution version of the image
      let artUrl = albumArtElement.src;
      // Sometimes the art URL has a size parameter - replace it with a higher quality
      artUrl = artUrl.replace(/=w[0-9]+-h[0-9]+/, '=w1080-h1080');
      songInfo.albumArt = artUrl;
    }
    
    // Get time information
    const timeInfo = getTimeInfo();
    songInfo.duration = timeInfo.duration;
    songInfo.position = timeInfo.position;
    
    return songInfo;
  } catch (error) {
    console.error("Error extracting song info:", error);
    return songInfo;
  }
}

// Get playback state (playing or paused)
function getPlaybackState() {
  try {
    const playPauseButton = document.querySelector('.ytmusic-player-bar play-pause-button');
    if (playPauseButton) {
      // Check the aria-label of the button - it says "Pause" when playing and "Play" when paused
      const buttonState = playPauseButton.getAttribute('title') || 
                          playPauseButton.getAttribute('aria-label') ||
                          '';
      
      return buttonState.includes('Pause') ? 'playing' : 'paused';
    }
    return 'paused'; // Default to paused if we can't determine
  } catch (error) {
    console.error("Error getting playback state:", error);
    return 'paused';
  }
}

// Extract time information from the player
function getTimeInfo() {
  const result = {
    position: 0,
    duration: 0
  };
  
  try {
    const timeDisplay = document.querySelector('.ytmusic-player-bar .time-info');
    if (timeDisplay) {
      const timeText = timeDisplay.textContent; // Format: "1:23 / 4:56"
      const timeParts = timeText.split('/');
      
      if (timeParts.length === 2) {
        // Convert "1:23" to seconds (83)
        const currentTime = convertTimeToSeconds(timeParts[0].trim());
        const totalTime = convertTimeToSeconds(timeParts[1].trim());
        
        result.position = currentTime;
        result.duration = totalTime;
      }
    }
  } catch (error) {
    console.error("Error extracting time info:", error);
  }
  
  return result;
}

// Convert time string (e.g., "1:23") to seconds
function convertTimeToSeconds(timeString) {
  try {
    const parts = timeString.split(':').map(part => parseInt(part.trim(), 10));
    
    if (parts.length === 3) { // Format: h:mm:ss
      return parts[0] * 3600 + parts[1] * 60 + parts[2];
    } else if (parts.length === 2) { // Format: m:ss
      return parts[0] * 60 + parts[1];
    } else {
      return 0;
    }
  } catch (error) {
    return 0;
  }
}

// Create activity data for Discord Rich Presence
function createActivityData(songInfo) {
  const now = Date.now();
  
  // Create timestamps object
  let timestamps = {};
  if (songInfo.duration > 0 && songInfo.position > 0) {
    // Calculate when the song started
    const startTime = now - (songInfo.position * 1000);
    // Calculate when the song will end
    const endTime = startTime + (songInfo.duration * 1000);
    
    timestamps.start = Math.floor(startTime / 1000);
    timestamps.end = Math.floor(endTime / 1000);
  }
  
  return {
    title: songInfo.title,
    artist: songInfo.artist,
    album: songInfo.album,
    albumArt: songInfo.albumArt,
    songUrl: songInfo.songUrl,
    timestamps: timestamps
  };
}

// Listen for messages from the extension
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.type) {
    case "getCurrentSong":
      sendResponse({
        song: currentSong,
        state: currentState
      });
      break;
    case "refreshNow":
      checkForSongUpdate();
      sendResponse({ success: true });
      break;
  }
  return true; // Keep the message channel open for async response
});

// Initialize when page is fully loaded
if (document.readyState === 'complete') {
  initialize();
} else {
  window.addEventListener('load', initialize);
}