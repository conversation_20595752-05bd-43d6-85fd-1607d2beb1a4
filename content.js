// Discord Rich Presence for YouTube Music - Content Script

// Configuration
const UPDATE_INTERVAL = 5000; // Check for new song every 5 seconds
let currentSong = null;
let currentState = null;
let intervalId = null;
let lastUpdate = null;

// Start the extension
function initialize() {
  console.log("YouTube Music Discord Rich Presence: Content script initialized");

  // Setup mutation observer to detect page changes
  setupMutationObserver();

  // Start the interval for regular checks
  startTrackingInterval();
}

// Set up mutation observer to detect DOM changes
function setupMutationObserver() {
  const observer = new MutationObserver((mutations) => {
    // Check if there were significant changes that might indicate song change
    const relevantChanges = mutations.some(mutation => {
      return mutation.target.classList &&
        (mutation.target.classList.contains('content-info-wrapper') ||
         mutation.target.classList.contains('ytmusic-player-bar'));
    });

    if (relevantChanges) {
      checkForSongUpdate();
    }
  });

  // Observe the player bar for changes
  const playerBar = document.querySelector('ytmusic-player-bar');
  if (playerBar) {
    observer.observe(playerBar, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'src', 'style']
    });
  }
}

// Start tracking interval
function startTrackingInterval() {
  if (intervalId) clearInterval(intervalId);

  // Initial check
  checkForSongUpdate();

  // Set up interval for regular checks
  intervalId = setInterval(checkForSongUpdate, UPDATE_INTERVAL);
}

// Check if song information has changed
function checkForSongUpdate() {
  try {
    const songInfo = extractSongInfo();
    const playbackState = getPlaybackState();

    // Check if song information has changed
    if (songInfo.title !== currentSong?.title || songInfo.artist !== currentSong?.artist || playbackState !== currentState) {
      // If song or playback state changed, update Discord presence
      if (shouldUpdatePresence(songInfo, playbackState)) {
        lastUpdate = Date.now();
        currentSong = songInfo;
        currentState = playbackState;

        if (playbackState === 'playing') {
          const activityData = createActivityData(songInfo);
          chrome.runtime.sendMessage({
            type: "updateActivity",
            data: activityData
          });
          console.log("Updated Discord presence with new song:", songInfo.title);
        } else {
          chrome.runtime.sendMessage({
            type: "clearActivity"
          });
          console.log("Cleared Discord presence - music paused");
        }
      }
    }
  } catch (error) {
    console.error("Error updating song info:", error);
  }
}

// Determine if we should send a presence update
function shouldUpdatePresence(songInfo, playbackState) {
  // Send update if any of these conditions are true:

  // 1. First time checking or previous check failed
  if (!currentSong || !currentState) return true;

  // 2. Playback state changed (playing/paused)
  if (currentState !== playbackState) return true;

  // 3. Song changed
  if (currentSong.title !== songInfo.title ||
      currentSong.artist !== songInfo.artist) return true;

  // 4. Significant time passed since last update (3 minutes)
  if (Date.now() - lastUpdate > 180000) return true;

  return false;
}

// Extract song information from the YouTube Music player
function extractSongInfo() {
  const songInfo = {
    title: "",
    artist: "",
    album: "",
    albumArt: "",
    songUrl: window.location.href,
    duration: 0,
    position: 0,
    // Enhanced information
    year: "",
    genre: "",
    playlistName: "",
    queuePosition: 0,
    queueTotal: 0,
    viewCount: "",
    likeCount: "",
    isLiked: false,
    isDisliked: false,
    repeatMode: "off", // off, one, all
    shuffleMode: false,
    volume: 100,
    quality: "",
    nextSong: "",
    previousSong: "",
    playbackRate: 1.0,
    isLive: false,
    channelName: "",
    channelUrl: "",
    videoId: "",
    playlistId: ""
  };

  try {
    // Multiple selectors for song title (YouTube Music changes these frequently)
    const titleSelectors = [
      'ytmusic-player-bar .title.ytmusic-player-bar',
      '.ytmusic-player-bar .title',
      'ytmusic-player-bar .content-info-wrapper .title',
      '#layout-player .title',
      '.player-bar-wrapper .title'
    ];

    for (const selector of titleSelectors) {
      const titleElement = document.querySelector(selector);
      if (titleElement && titleElement.textContent.trim()) {
        songInfo.title = titleElement.textContent.trim();
        break;
      }
    }

    // Multiple selectors for artist name
    const artistSelectors = [
      'ytmusic-player-bar .byline a[href*="/channel/"]',
      'ytmusic-player-bar .byline .yt-simple-endpoint',
      '.ytmusic-player-bar .byline a',
      'ytmusic-player-bar .subtitle a',
      '.player-bar-wrapper .byline a'
    ];

    for (const selector of artistSelectors) {
      const artistElement = document.querySelector(selector);
      if (artistElement && artistElement.textContent.trim()) {
        songInfo.artist = artistElement.textContent.trim();
        break;
      }
    }

    // Get album name from byline
    const bylineSelectors = [
      'ytmusic-player-bar .byline',
      'ytmusic-player-bar .subtitle',
      '.player-bar-wrapper .byline'
    ];

    for (const selector of bylineSelectors) {
      const bylineElement = document.querySelector(selector);
      if (bylineElement) {
        const bylineText = bylineElement.textContent;
        // Look for album after bullet point
        const albumMatch = bylineText.match(/•\s*([^•]+?)(?:\s*•|$)/);
        if (albumMatch && albumMatch[1] && albumMatch[1].trim() !== songInfo.artist) {
          songInfo.album = albumMatch[1].trim();
          break;
        }
      }
    }

    // Multiple selectors for album art
    const artSelectors = [
      'ytmusic-player-bar .image',
      'ytmusic-player-bar img',
      '.player-bar-wrapper .image',
      '#layout-player .image img',
      'ytmusic-player-bar .thumbnail img'
    ];

    for (const selector of artSelectors) {
      const albumArtElement = document.querySelector(selector);
      if (albumArtElement && albumArtElement.src) {
        let artUrl = albumArtElement.src;
        // Get highest resolution version
        artUrl = artUrl.replace(/=w[0-9]+-h[0-9]+/, '=w1080-h1080');
        artUrl = artUrl.replace(/=s[0-9]+/, '=s1080');
        songInfo.albumArt = artUrl;
        break;
      }
    }

    // Get time information
    const timeInfo = getTimeInfo();
    songInfo.duration = timeInfo.duration;
    songInfo.position = timeInfo.position;

    console.log("Extracted song info:", songInfo);
    return songInfo;
  } catch (error) {
    console.error("Error extracting song info:", error);
    return songInfo;
  }
}

// Get playback state (playing or paused)
function getPlaybackState() {
  try {
    // Multiple selectors for play/pause button
    const buttonSelectors = [
      'ytmusic-player-bar .play-pause-button',
      'ytmusic-player-bar tp-yt-paper-icon-button[data-title-no-tooltip]',
      '.ytmusic-player-bar [aria-label*="Play"], .ytmusic-player-bar [aria-label*="Pause"]',
      '#play-pause-button',
      '.player-controls .play-pause-button'
    ];

    for (const selector of buttonSelectors) {
      const playPauseButton = document.querySelector(selector);
      if (playPauseButton) {
        // Check various attributes that might indicate state
        const title = playPauseButton.getAttribute('title') || '';
        const ariaLabel = playPauseButton.getAttribute('aria-label') || '';
        const dataTitle = playPauseButton.getAttribute('data-title-no-tooltip') || '';

        const buttonState = (title + ' ' + ariaLabel + ' ' + dataTitle).toLowerCase();

        // If it says "pause", music is playing. If it says "play", music is paused
        if (buttonState.includes('pause')) {
          return 'playing';
        } else if (buttonState.includes('play')) {
          return 'paused';
        }

        // Check for icon classes as fallback
        const iconElement = playPauseButton.querySelector('svg, iron-icon, tp-yt-iron-icon');
        if (iconElement) {
          const iconClasses = iconElement.className || '';
          if (iconClasses.includes('pause')) {
            return 'playing';
          } else if (iconClasses.includes('play')) {
            return 'paused';
          }
        }
      }
    }

    // Fallback: check if there's any indication of audio playing
    const progressBar = document.querySelector('ytmusic-player-bar .progress-bar, .ytmusic-player-bar .slider');
    if (progressBar) {
      // If progress bar is moving or has recent activity, assume playing
      const progressValue = progressBar.getAttribute('aria-valuenow') || progressBar.value;
      if (progressValue && parseFloat(progressValue) > 0) {
        return 'playing';
      }
    }

    return 'paused'; // Default to paused if we can't determine
  } catch (error) {
    console.error("Error getting playback state:", error);
    return 'paused';
  }
}

// Extract time information from the player
function getTimeInfo() {
  const result = {
    position: 0,
    duration: 0
  };

  try {
    // Multiple selectors for time display
    const timeSelectors = [
      'ytmusic-player-bar .time-info',
      '.ytmusic-player-bar .time-info',
      'ytmusic-player-bar .time-display',
      '.player-bar-wrapper .time-info',
      '#time-info'
    ];

    for (const selector of timeSelectors) {
      const timeDisplay = document.querySelector(selector);
      if (timeDisplay && timeDisplay.textContent) {
        const timeText = timeDisplay.textContent; // Format: "1:23 / 4:56"
        const timeParts = timeText.split('/');

        if (timeParts.length === 2) {
          // Convert "1:23" to seconds (83)
          const currentTime = convertTimeToSeconds(timeParts[0].trim());
          const totalTime = convertTimeToSeconds(timeParts[1].trim());

          if (currentTime >= 0 && totalTime > 0) {
            result.position = currentTime;
            result.duration = totalTime;
            break;
          }
        }
      }
    }

    // Fallback: try to get time from progress bar attributes
    if (result.duration === 0) {
      const progressBar = document.querySelector('ytmusic-player-bar .progress-bar, ytmusic-player-bar input[type="range"]');
      if (progressBar) {
        const max = parseFloat(progressBar.getAttribute('aria-valuemax') || progressBar.max || 0);
        const now = parseFloat(progressBar.getAttribute('aria-valuenow') || progressBar.value || 0);

        if (max > 0) {
          result.duration = max;
          result.position = now;
        }
      }
    }
  } catch (error) {
    console.error("Error extracting time info:", error);
  }

  return result;
}

// Convert time string (e.g., "1:23") to seconds
function convertTimeToSeconds(timeString) {
  try {
    const parts = timeString.split(':').map(part => parseInt(part.trim(), 10));

    if (parts.length === 3) { // Format: h:mm:ss
      return parts[0] * 3600 + parts[1] * 60 + parts[2];
    } else if (parts.length === 2) { // Format: m:ss
      return parts[0] * 60 + parts[1];
    } else {
      return 0;
    }
  } catch (error) {
    return 0;
  }
}

// Create activity data for Discord Rich Presence
function createActivityData(songInfo) {
  const now = Date.now();

  // Create timestamps object for progress bar
  let timestamps = {};
  if (songInfo.duration > 0 && songInfo.position >= 0) {
    // Calculate when the song started (more accurate)
    const startTime = now - (songInfo.position * 1000);
    // Calculate when the song will end
    const endTime = startTime + (songInfo.duration * 1000);

    timestamps.start = Math.floor(startTime / 1000);
    timestamps.end = Math.floor(endTime / 1000);
  }

  // Enhanced activity data with more information
  const activityData = {
    title: songInfo.title,
    artist: songInfo.artist,
    album: songInfo.album,
    albumArt: songInfo.albumArt,
    songUrl: songInfo.songUrl,
    timestamps: timestamps,

    // Additional metadata for enhanced presence
    duration: songInfo.duration,
    position: songInfo.position,
    year: songInfo.year,
    genre: songInfo.genre,
    playlistName: songInfo.playlistName,
    queuePosition: songInfo.queuePosition,
    queueTotal: songInfo.queueTotal,
    viewCount: songInfo.viewCount,
    likeCount: songInfo.likeCount,
    isLiked: songInfo.isLiked,
    isDisliked: songInfo.isDisliked,
    repeatMode: songInfo.repeatMode,
    shuffleMode: songInfo.shuffleMode,
    volume: songInfo.volume,
    quality: songInfo.quality,
    nextSong: songInfo.nextSong,
    previousSong: songInfo.previousSong,
    playbackRate: songInfo.playbackRate,
    isLive: songInfo.isLive,
    channelName: songInfo.channelName,
    channelUrl: songInfo.channelUrl,
    videoId: songInfo.videoId,
    playlistId: songInfo.playlistId
  };

  // Add progress percentage for additional display options
  if (songInfo.duration > 0 && songInfo.position >= 0) {
    activityData.progressPercent = Math.round((songInfo.position / songInfo.duration) * 100);
  }

  // Format duration and position for display
  activityData.durationFormatted = formatTime(songInfo.duration);
  activityData.positionFormatted = formatTime(songInfo.position);

  return activityData;
}

// Format time in seconds to MM:SS or HH:MM:SS
function formatTime(seconds) {
  if (!seconds || seconds < 0) return "0:00";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
}

// Listen for messages from the extension
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.type) {
    case "getCurrentSong":
      sendResponse({
        song: currentSong,
        state: currentState
      });
      break;
    case "refreshNow":
      checkForSongUpdate();
      sendResponse({ success: true });
      break;
  }
  return true; // Keep the message channel open for async response
});

// Initialize when page is fully loaded
if (document.readyState === 'complete') {
  initialize();
} else {
  window.addEventListener('load', initialize);
}
