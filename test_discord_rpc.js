const DiscordRPC = require('discord-rpc');

console.log('Testing Discord RPC connection...');

const clientId = '1375799244400623726';
const rpc = new DiscordRPC.Client({ transport: 'ipc' });

rpc.on('ready', () => {
  console.log('✅ Successfully connected to Discord!');
  console.log('User:', rpc.user.username);
  
  // Set a test activity
  rpc.setActivity({
    details: 'Testing RPC Connection',
    state: 'From Node.js',
    assets: {
      large_image: 'ytmusic_logo',
      large_text: 'YouTube Music'
    }
  }).then(() => {
    console.log('✅ Activity set successfully!');
    console.log('Check your Discord profile - you should see the activity');
    
    setTimeout(() => {
      console.log('Clearing activity and disconnecting...');
      rpc.clearActivity();
      rpc.destroy();
      process.exit(0);
    }, 10000);
  }).catch(error => {
    console.error('❌ Error setting activity:', error.message);
    rpc.destroy();
    process.exit(1);
  });
});

rpc.on('disconnected', () => {
  console.log('Disconnected from Discord');
});

rpc.login({ clientId }).catch(error => {
  console.error('❌ Failed to connect to Discord:', error.message);
  console.log('\nPossible issues:');
  console.log('1. Discord/Vencord is not running');
  console.log('2. Discord RPC is disabled in settings');
  console.log('3. Vencord is blocking RPC connections');
  console.log('4. discord-rpc package is not installed');
  process.exit(1);
});

console.log('Attempting to connect to Discord...');
console.log('Make sure Discord or Vencord is running!');
