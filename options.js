// Discord Rich Presence for YouTube Music - Options Script

// DOM Elements
const statusDot = document.getElementById('status-dot');
const statusText = document.getElementById('status-text');
const reconnectButton = document.getElementById('reconnect-button');

// Settings elements
const enablePresenceToggle = document.getElementById('enable-presence');
const privateModeToggle = document.getElementById('private-mode');
const autoConnectToggle = document.getElementById('auto-connect');

const showTitleToggle = document.getElementById('show-title');
const showArtistToggle = document.getElementById('show-artist');
const showAlbumToggle = document.getElementById('show-album');
const showArtworkToggle = document.getElementById('show-artwork');
const showTimeToggle = document.getElementById('show-time');
const showButtonToggle = document.getElementById('show-button');

const updateFrequencySelect = document.getElementById('update-frequency');

const resetButton = document.getElementById('reset-button');
const saveButton = document.getElementById('save-button');

// Default settings
const DEFAULT_SETTINGS = {
  enabled: true,
  privateMode: false,
  autoConnect: true,
  showSongTitle: true,
  showArtist: true,
  showAlbum: true,
  showTimestamp: true,
  showAlbumArt: true,
  showButton: true,
  updateFrequency: 15000
};

// Initialize the options page
document.addEventListener('DOMContentLoaded', () => {
  // Load saved settings
  loadSettings();
  
  // Check connection status
  checkConnectionStatus();
  
  // Set up event listeners
  setupEventListeners();
  
  // Set up the preview updates
  setupPreviewUpdates();
});

// Check Discord connection status
function checkConnectionStatus() {
  chrome.runtime.sendMessage({ type: 'getConnectionStatus' }, (response) => {
    if (response && response.connected) {
      updateConnectionStatus(true);
    } else {
      updateConnectionStatus(false);
    }
  });
}

// Update connection status UI
function updateConnectionStatus(connected) {
  if (connected) {
    statusDot.classList.remove('disconnected');
    statusDot.classList.add('connected');
    statusText.textContent = 'Connected to Discord';
  } else {
    statusDot.classList.remove('connected');
    statusDot.classList.add('disconnected');
    statusText.textContent = 'Disconnected from Discord';
  }
}

// Load user settings
function loadSettings() {
  chrome.storage.sync.get(DEFAULT_SETTINGS, (settings) => {
    // Apply loaded settings to UI
    enablePresenceToggle.checked = settings.enabled;
    privateModeToggle.checked = settings.privateMode;
    autoConnectToggle.checked = settings.autoConnect;
    
    showTitleToggle.checked = settings.showSongTitle;
    showArtistToggle.checked = settings.showArtist;
    showAlbumToggle.checked = settings.showAlbum;
    showArtworkToggle.checked = settings.showAlbumArt;
    showTimeToggle.checked = settings.showTimestamp;
    showButtonToggle.checked = settings.showButton;
    
    updateFrequencySelect.value = settings.updateFrequency.toString();
    
    // Update the preview to match loaded settings
    updatePreview(settings);
  });
}

// Save user settings
function saveSettings() {
  // Collect settings from UI
  const settings = {
    enabled: enablePresenceToggle.checked,
    privateMode: privateModeToggle.checked,
    autoConnect: autoConnectToggle.checked,
    showSongTitle: showTitleToggle.checked,
    showArtist: showArtistToggle.checked,
    showAlbum: showAlbumToggle.checked,
    showTimestamp: showTimeToggle.checked,
    showAlbumArt: showArtworkToggle.checked,
    showButton: showButtonToggle.checked,
    updateFrequency: parseInt(updateFrequencySelect.value, 10)
  };
  
  // Save to storage
  chrome.storage.sync.set(settings, () => {
    // Show saved notification
    saveButton.textContent = 'Saved!';
    setTimeout(() => {
      saveButton.textContent = 'Save Settings';
    }, 2000);
    
    // Notify content script to refresh with new settings
    chrome.tabs.query({ url: '*://music.youtube.com/*' }, (tabs) => {
      if (tabs.length > 0) {
        chrome.tabs.sendMessage(tabs[0].id, { type: 'refreshNow' });
      }
    });
  });
}

// Reset settings to defaults
function resetToDefaults() {
  // Apply default settings to UI
  enablePresenceToggle.checked = DEFAULT_SETTINGS.enabled;
  privateModeToggle.checked = DEFAULT_SETTINGS.privateMode;
  autoConnectToggle.checked = DEFAULT_SETTINGS.autoConnect;
  
  showTitleToggle.checked = DEFAULT_SETTINGS.showSongTitle;
  showArtistToggle.checked = DEFAULT_SETTINGS.showArtist;
  showAlbumToggle.checked = DEFAULT_SETTINGS.showAlbum;
  showArtworkToggle.checked = DEFAULT_SETTINGS.showAlbumArt;
  showTimeToggle.checked = DEFAULT_SETTINGS.showTimestamp;
  showButtonToggle.checked = DEFAULT_SETTINGS.showButton;
  
  updateFrequencySelect.value = DEFAULT_SETTINGS.updateFrequency.toString();
  
  // Update preview
  updatePreview(DEFAULT_SETTINGS);
  
  // Show reset notification
  resetButton.textContent = 'Defaults Restored';
  setTimeout(() => {
    resetButton.textContent = 'Reset to Defaults';
  }, 2000);
}

// Set up event listeners
function setupEventListeners() {
  // Save button
  saveButton.addEventListener('click', saveSettings);
  
  // Reset button
  resetButton.addEventListener('click', resetToDefaults);
  
  // Reconnect button
  reconnectButton.addEventListener('click', reconnectToDiscord);
  
  // Private mode affects display options
  privateModeToggle.addEventListener('change', function() {
    const isPrivate = this.checked;
    
    // When private mode is on, disable some display options
    showTitleToggle.disabled = isPrivate;
    showArtistToggle.disabled = isPrivate;
    showAlbumToggle.disabled = isPrivate;
    showArtworkToggle.disabled = isPrivate;
    
    updatePreviewFromInputs();
  });
  
  // Enable presence affects all other settings
  enablePresenceToggle.addEventListener('change', function() {
    const isEnabled = this.checked;
    
    // Enable/disable all settings based on main toggle
    privateModeToggle.disabled = !isEnabled;
    autoConnectToggle.disabled = !isEnabled;
    showTitleToggle.disabled = !isEnabled || privateModeToggle.checked;
    showArtistToggle.disabled = !isEnabled || privateModeToggle.checked;
    showAlbumToggle.disabled = !isEnabled || privateModeToggle.checked;
    showArtworkToggle.disabled = !isEnabled || privateModeToggle.checked;
    showTimeToggle.disabled = !isEnabled;
    showButtonToggle.disabled = !isEnabled;
    updateFrequencySelect.disabled = !isEnabled;
    
    updatePreviewFromInputs();
  });
}

// Set up live preview updates based on settings changes
function setupPreviewUpdates() {
  // Update preview when any setting changes
  const allSettingsInputs = [
    enablePresenceToggle, privateModeToggle, 
    showTitleToggle, showArtistToggle, showAlbumToggle, 
    showArtworkToggle, showTimeToggle, showButtonToggle
  ];
  
  // Add change listeners to all inputs
  allSettingsInputs.forEach(input => {
    input.addEventListener('change', updatePreviewFromInputs);
  });
  
  updateFrequencySelect.addEventListener('change', updatePreviewFromInputs);
}

// Update preview based on current input values
function updatePreviewFromInputs() {
  const settings = {
    enabled: enablePresenceToggle.checked,
    privateMode: privateModeToggle.checked,
    showSongTitle: showTitleToggle.checked,
    showArtist: showArtistToggle.checked,
    showAlbum: showAlbumToggle.checked,
    showAlbumArt: showArtworkToggle.checked,
    showTimestamp: showTimeToggle.checked,
    showButton: showButtonToggle.checked
  };
  
  updatePreview(settings);
}

// Update the preview based on settings
function updatePreview(settings) {
  const previewCard = document.querySelector('.discord-preview');
  
  // If disabled, show disabled state
  if (!settings.enabled) {
    previewCard.innerHTML = `
      <div class="playing-status">Discord Rich Presence is disabled</div>
    `;
    return;
  }
  
  // Show private mode or regular display
  if (settings.privateMode) {
    previewCard.innerHTML = `
      <div class="playing-status">Playing YouTube Music</div>
      <div class="activity-card">
        <img src="img-proxy-1.webp" alt="YouTube Music Logo" class="activity-image">
        <div class="activity-details">
          <div class="activity-name">YouTube Music</div>
          <div class="activity-artist">Private Mode</div>
          ${settings.showTimestamp ? '<div class="activity-time">3:45 elapsed</div>' : ''}
        </div>
      </div>
    `;
  } else {
    previewCard.innerHTML = `
      <div class="playing-status">Playing YouTube Music</div>
      <div class="activity-card">
        ${settings.showAlbumArt ? 
          '<img src="img-proxy-1.webp" alt="Album Art" class="activity-image">' : 
          '<img src="img-proxy-1.webp" alt="YouTube Music Logo" class="activity-image">'}
        <div class="activity-details">
          ${settings.showSongTitle ? '<div class="activity-name">Song Title</div>' : ''}
          ${settings.showArtist ? '<div class="activity-artist">by Artist</div>' : ''}
          ${settings.showTimestamp ? '<div class="activity-time">3:45 elapsed</div>' : ''}
        </div>
      </div>
      ${settings.showButton ? 
        '<div class="activity-buttons"><div class="activity-button">Listen on YouTube Music</div></div>' : ''}
    `;
  }
}

// Reconnect to Discord
function reconnectToDiscord() {
  reconnectButton.textContent = 'Connecting...';
  reconnectButton.disabled = true;
  
  chrome.runtime.sendMessage({ type: 'forceReconnect' });
  
  // Give some time for reconnection attempt
  setTimeout(() => {
    checkConnectionStatus();
    reconnectButton.textContent = 'Reconnect';
    reconnectButton.disabled = false;
  }, 2000);
}