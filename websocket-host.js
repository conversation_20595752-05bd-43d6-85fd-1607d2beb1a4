const DiscordRPC = require('discord-rpc');
const WebSocket = require('ws');
const fs = require('fs');
const path = require('path');

// Configuration
const clientId = '1375799244400623726';
const WS_PORT = 8765;

// State
let rpc = null;
let discordConnected = false;
let wsServer = null;
let connectedClients = new Set();

// Logging function
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  
  // Log to file
  try {
    const logPath = path.join(process.env.USERPROFILE || process.env.HOME, 'ytmusic-rpc-debug.log');
    fs.appendFileSync(logPath, logMessage);
  } catch (e) {
    // Ignore file write errors
  }
  
  // Log to console
  console.log(`[RPC-SERVER] ${message}`);
}

log('YouTube Music Discord RPC WebSocket Server starting...');

// Broadcast message to all connected clients
function broadcast(message) {
  const data = JSON.stringify(message);
  connectedClients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      try {
        client.send(data);
        log(`Sent to client: ${data}`);
      } catch (error) {
        log(`Error sending to client: ${error.message}`);
      }
    }
  });
}

// Discord connection
function connectToDiscord() {
  if (discordConnected) return Promise.resolve();
  
  log('Attempting to connect to Discord...');
  
  return new Promise((resolve, reject) => {
    // Create new RPC client
    rpc = new DiscordRPC.Client({ transport: 'ipc' });
    
    rpc.on('ready', () => {
      log('Successfully connected to Discord');
      discordConnected = true;
      broadcast({ event: 'discord_connected' });
      resolve();
    });
    
    rpc.on('disconnected', () => {
      log('Disconnected from Discord');
      discordConnected = false;
      broadcast({ event: 'discord_disconnected' });
    });
    
    // Try to login
    rpc.login({ clientId })
      .catch(error => {
        log(`Discord login failed: ${error.message}`);
        discordConnected = false;
        broadcast({ event: 'discord_connection_failed', error: error.message });
        reject(error);
      });
  });
}

// Handle activity updates
function setActivity(activity) {
  if (!discordConnected) {
    log('Cannot set activity: not connected to Discord');
    broadcast({ event: 'error', message: 'Not connected to Discord' });
    return;
  }
  
  if (!activity) {
    log('Clearing Discord activity');
    rpc.clearActivity()
      .then(() => {
        log('Activity cleared successfully');
        broadcast({ event: 'activity_cleared' });
      })
      .catch(error => {
        log(`Error clearing activity: ${error.message}`);
        broadcast({ event: 'error', message: error.message });
      });
    return;
  }
  
  log(`Setting Discord activity: ${JSON.stringify(activity)}`);
  rpc.setActivity(activity)
    .then(() => {
      log('Activity set successfully');
      broadcast({ event: 'activity_set', activity });
    })
    .catch(error => {
      log(`Error setting activity: ${error.message}`);
      broadcast({ event: 'error', message: error.message });
    });
}

// Handle WebSocket messages
function handleWebSocketMessage(ws, message) {
  try {
    const data = JSON.parse(message);
    log(`Received WebSocket message: ${message}`);
    
    switch (data.type) {
      case 'connect_discord':
        connectToDiscord()
          .then(() => {
            ws.send(JSON.stringify({ event: 'discord_connected' }));
          })
          .catch(error => {
            ws.send(JSON.stringify({ event: 'discord_connection_failed', error: error.message }));
          });
        break;
        
      case 'set_activity':
        setActivity(data.activity);
        break;
        
      case 'clear_activity':
        setActivity(null);
        break;
        
      case 'get_status':
        ws.send(JSON.stringify({
          event: 'status',
          discord_connected: discordConnected,
          clients_connected: connectedClients.size
        }));
        break;
        
      default:
        log(`Unknown message type: ${data.type}`);
        ws.send(JSON.stringify({ event: 'error', message: 'Unknown message type' }));
    }
  } catch (error) {
    log(`Error handling WebSocket message: ${error.message}`);
    ws.send(JSON.stringify({ event: 'error', message: 'Invalid message format' }));
  }
}

// Create WebSocket server
function createWebSocketServer() {
  wsServer = new WebSocket.Server({ port: WS_PORT });
  
  wsServer.on('listening', () => {
    log(`WebSocket server listening on port ${WS_PORT}`);
  });
  
  wsServer.on('connection', (ws) => {
    log('New WebSocket client connected');
    connectedClients.add(ws);
    
    // Send current status to new client
    ws.send(JSON.stringify({
      event: 'connected',
      discord_connected: discordConnected,
      server_version: '2.0.0'
    }));
    
    ws.on('message', (message) => {
      handleWebSocketMessage(ws, message);
    });
    
    ws.on('close', () => {
      log('WebSocket client disconnected');
      connectedClients.delete(ws);
    });
    
    ws.on('error', (error) => {
      log(`WebSocket client error: ${error.message}`);
      connectedClients.delete(ws);
    });
  });
  
  wsServer.on('error', (error) => {
    log(`WebSocket server error: ${error.message}`);
  });
}

// Graceful shutdown
function shutdown() {
  log('Shutting down...');
  
  if (rpc && discordConnected) {
    rpc.destroy();
  }
  
  if (wsServer) {
    wsServer.close();
  }
  
  process.exit(0);
}

process.on('SIGINT', shutdown);
process.on('SIGTERM', shutdown);

// Start the server
log('Starting WebSocket server...');
createWebSocketServer();

log('Attempting initial Discord connection...');
connectToDiscord()
  .then(() => {
    log('Server initialized successfully');
  })
  .catch(error => {
    log(`Initial Discord connection failed: ${error.message}`);
    log('Server will continue running, Discord connection can be retried via WebSocket');
  });

log('Server setup complete, waiting for connections...');
