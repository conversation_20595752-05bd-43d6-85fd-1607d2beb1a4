# 🔧 Vencord Troubleshooting Guide for YouTube Music Discord Rich Presence

## 🚨 **Quick Fix Steps**

### **Step 1: Install Dependencies**
```bash
cd "%USERPROFILE%\YTMusicDiscordRP"
npm install
```

### **Step 2: Run Registry Setup as Administrator**
1. Right-click on `setup_registry.bat`
2. Select **"Run as administrator"**
3. Follow the prompts

### **Step 3: Get Your Extension ID**
1. Go to `chrome://extensions/`
2. Enable **Developer mode**
3. Find your extension and copy the **ID**
4. Update `native_manifest.json` with your actual extension ID

### **Step 4: Test Native Messaging**
```bash
cd "%USERPROFILE%\YTMusicDiscordRP"
node host.js
```
Should not crash immediately. Press `Ctrl+C` to exit.

---

## 🔍 **Detailed Troubleshooting**

### **Issue 1: "Disconnected from Discord" in Vencord**

**Cause:** Vencord uses different IPC paths than regular Discord.

**Solution:**
1. **Check if Vencord is running:** Make sure Vencord is open and logged in
2. **Verify IPC paths:** The `host.js` already includes Vencord-specific paths
3. **Check Discord settings in Vencord:**
   - Go to Settings → Activity Settings
   - Enable **"Display current activity as a status message"**
   - Enable **"Share your detected activities with others"**

### **Issue 2: Extension Not Detecting Songs**

**Debug Steps:**
1. Open YouTube Music
2. Press `F12` to open DevTools
3. Go to **Console** tab
4. Copy and paste the contents of `debug_extension.js`
5. Check the output for errors

**Common Fixes:**
- YouTube Music changed their DOM structure
- Content script not injecting properly
- Selectors need updating

### **Issue 3: Native Messaging Host Not Found**

**Check Registry:**
```cmd
reg query "HKEY_CURRENT_USER\Software\Google\Chrome\NativeMessagingHosts\com.discord.rich_presence"
```

**Should return:** Path to your `com.discord.rich_presence.json`

**If not found:**
1. Run `setup_registry.bat` as Administrator
2. Check that `com.discord.rich_presence.json` exists
3. Verify the path in the JSON file is correct

### **Issue 4: Node.js or Dependencies Missing**

**Install Node.js:**
1. Download from [nodejs.org](https://nodejs.org/)
2. Install with default settings
3. Restart command prompt
4. Test: `node --version`

**Install Dependencies:**
```bash
cd "%USERPROFILE%\YTMusicDiscordRP"
npm install discord-rpc ws
```

### **Issue 5: Wrong Extension ID**

**Get Correct ID:**
1. Load extension in Chrome
2. Go to `chrome://extensions/`
3. Copy the ID under your extension
4. Update `native_manifest.json`:
```json
{
  "allowed_origins": [
    "chrome-extension://YOUR_ACTUAL_EXTENSION_ID/"
  ]
}
```

---

## 🧪 **Testing Steps**

### **Test 1: Manual Host Test**
```bash
cd "%USERPROFILE%\YTMusicDiscordRP"
node host.js
```
- Should start without errors
- Check log file: `%USERPROFILE%\ytmusic-rpc-log.txt`

### **Test 2: Extension Console**
1. Go to `chrome://extensions/`
2. Click **"Inspect views: service worker"** for your extension
3. Check for errors in console
4. Look for connection attempts

### **Test 3: Content Script Test**
1. Open YouTube Music
2. Press `F12` → Console
3. Type: `chrome.runtime.sendMessage({type: 'getConnectionStatus'}, console.log)`
4. Should return connection status

### **Test 4: Vencord Compatibility**
1. Close Vencord completely
2. Open regular Discord
3. Test if extension works with regular Discord
4. If it works, the issue is Vencord-specific

---

## 🔧 **Advanced Fixes**

### **Fix 1: Update Vencord IPC Detection**
Edit `host.js` and add more Vencord paths:
```javascript
// Add to findVencordIPCPath function
possiblePaths = [
  '\\\\?\\pipe\\discord-ipc-0',
  '\\\\?\\pipe\\vencord-ipc-0',
  '\\\\?\\pipe\\discord-ipc-1',  // Add this
  '\\\\?\\pipe\\vencord-ipc-1'   // Add this
];
```

### **Fix 2: Force Specific IPC Path**
If you know Vencord's IPC path, hardcode it:
```javascript
const rpc = new DiscordRPC.Client({ 
  transport: 'ipc',
  clientId: clientId,
  socketPath: '\\\\?\\pipe\\discord-ipc-0'  // Force specific path
});
```

### **Fix 3: Create New Discord Application**
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create new application
3. Copy Client ID
4. Update `CLIENT_ID` in `background.js` and `host.js`

---

## 📋 **Checklist**

- [ ] Node.js installed
- [ ] Dependencies installed (`npm install`)
- [ ] Registry setup run as Administrator
- [ ] Correct extension ID in `native_manifest.json`
- [ ] Vencord is running and logged in
- [ ] Discord activity settings enabled
- [ ] YouTube Music is open with a song playing
- [ ] Extension loaded in Chrome with Developer Mode

---

## 🆘 **Still Not Working?**

### **Collect Debug Information:**
1. Extension console errors
2. Content of `%USERPROFILE%\ytmusic-rpc-log.txt`
3. Output of `debug_extension.js` in YouTube Music console
4. Registry query results
5. Vencord version and settings

### **Alternative Solutions:**
1. **Use regular Discord** instead of Vencord temporarily
2. **Try different Discord RPC library** (discord.js instead of discord-rpc)
3. **Use WebSocket connection** instead of IPC
4. **Check for Vencord plugins** that might interfere with RPC

---

## 📞 **Getting Help**

If none of these solutions work:
1. Run all debug steps above
2. Collect the debug information
3. Create an issue with all the details
4. Include your Vencord version and any relevant plugins
