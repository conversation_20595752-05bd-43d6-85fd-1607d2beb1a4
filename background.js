// Discord Rich Presence for YouTube Music - Background Service Worker (WebSocket Version)

// Configuration
const CLIENT_ID = '1375799244400623726';
const WS_URL = 'ws://localhost:8765';
const discordPipe = { connected: false };
let websocket = null;
let currentActivity = null;
let lastUpdateTime = 0;
let connectionAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_INTERVAL = 5000; // 5 seconds
const UPDATE_THROTTLE = 15000; // 15 seconds (Discord rate limit is 1 update per 15 seconds)

// Initialize the extension
async function init() {
  try {
    // Load user settings
    const userSettings = await loadSettings();

    // Attempt to connect to Discord
    connectToDiscordClient();

    // Set up periodic connection check
    setInterval(checkDiscordConnection, 30000);
  } catch (error) {
    console.error("Failed to initialize extension:", error);
  }
}

// Connect to the Discord client via WebSocket
function connectToDiscordClient() {
  try {
    if (discordPipe.connected) return;

    connectionAttempts++;
    console.log(`Attempting to connect to WebSocket server (attempt ${connectionAttempts})`);

    // Close existing connection if any
    if (websocket) {
      websocket.close();
      websocket = null;
    }

    // Create WebSocket connection
    websocket = new WebSocket(WS_URL);

    websocket.onopen = () => {
      console.log("Connected to WebSocket server");
      // Request Discord connection
      websocket.send(JSON.stringify({ type: 'connect_discord' }));
    };

    websocket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        console.log("Received WebSocket message:", message);

        switch (message.event) {
          case 'discord_connected':
            console.log("Successfully connected to Discord via WebSocket");
            discordPipe.connected = true;
            connectionAttempts = 0;

            // Send initial presence update
            if (currentActivity) {
              updatePresence(currentActivity);
            }
            break;

          case 'discord_disconnected':
            console.log("Discord disconnected");
            discordPipe.connected = false;
            break;

          case 'discord_connection_failed':
            console.error("Discord connection failed:", message.error);
            discordPipe.connected = false;
            break;

          case 'activity_set':
            console.log("Activity set successfully");
            break;

          case 'error':
            console.error("WebSocket error:", message.message);
            break;
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    };

    websocket.onclose = () => {
      console.log("WebSocket connection closed");
      discordPipe.connected = false;
      websocket = null;

      // Attempt to reconnect if not too many attempts
      if (connectionAttempts < MAX_RECONNECT_ATTEMPTS) {
        setTimeout(() => connectToDiscordClient(), RECONNECT_INTERVAL);
      } else {
        console.error("Failed to connect to WebSocket server after multiple attempts");
      }
    };

    websocket.onerror = (error) => {
      console.error("WebSocket error:", error);
      discordPipe.connected = false;
    };

  } catch (error) {
    console.error("Error connecting to WebSocket:", error);
    discordPipe.connected = false;

    // Retry after delay if not too many attempts
    if (connectionAttempts < MAX_RECONNECT_ATTEMPTS) {
      setTimeout(() => connectToDiscordClient(), RECONNECT_INTERVAL);
    }
  }
}

// Check Discord connection and reconnect if needed
function checkDiscordConnection() {
  if (!discordPipe.connected) {
    connectionAttempts = 0; // Reset counter for scheduled checks
    connectToDiscordClient();
  }
}

// Load user settings from storage
async function loadSettings() {
  return new Promise((resolve) => {
    chrome.storage.sync.get({
      enabled: true,
      showSongTitle: true,
      showArtist: true,
      showAlbum: true,
      showTimestamp: true,
      showAlbumArt: true,
      privateMode: false
    }, (settings) => {
      resolve(settings);
    });
  });
}

// Update Discord Rich Presence with new activity data
async function updatePresence(activity) {
  try {
    // Check if throttling needed (Discord rate limit)
    const now = Date.now();
    if (now - lastUpdateTime < UPDATE_THROTTLE) {
      return;
    }

    // Load current user settings
    const settings = await loadSettings();

    // Don't update if extension is disabled or in private mode
    if (!settings.enabled) {
      clearPresence();
      return;
    }

    if (!discordPipe.connected) {
      connectToDiscordClient();
      return;
    }

    // Create presence data object based on user settings
    const presenceData = createPresenceData(activity, settings);

    // Send to Discord via WebSocket
    if (websocket && websocket.readyState === WebSocket.OPEN) {
      websocket.send(JSON.stringify({
        type: 'set_activity',
        activity: presenceData
      }));
    }

    lastUpdateTime = now;

    // Store current activity for reconnection purposes
    currentActivity = activity;

  } catch (error) {
    console.error("Error updating presence:", error);
  }
}

// Create the presence data object based on user settings
function createPresenceData(activity, settings) {
  if (settings.privateMode) {
    // Simplified presence for private mode
    return {
      details: "Listening to YouTube Music",
      state: "Private Mode",
      assets: {
        large_image: "ytmusic_logo",
        large_text: "YouTube Music"
      },
      timestamps: activity.timestamps
    };
  }

  const presenceData = {
    assets: {
      large_image: settings.showAlbumArt && activity.albumArt ? activity.albumArt : "ytmusic_logo",
      large_text: settings.showAlbum && activity.album ? activity.album : "YouTube Music"
    }
  };

  // Add details (song title)
  if (settings.showSongTitle && activity.title) {
    presenceData.details = activity.title.slice(0, 128);
  } else {
    presenceData.details = "Listening to YouTube Music";
  }

  // Add state (artist)
  if (settings.showArtist && activity.artist) {
    presenceData.state = activity.artist.slice(0, 128);
  }

  // Add timestamps
  if (settings.showTimestamp && activity.timestamps) {
    presenceData.timestamps = activity.timestamps;
  }

  // Add buttons if available
  if (activity.songUrl) {
    presenceData.buttons = [
      {
        label: "Listen on YouTube Music",
        url: activity.songUrl
      }
    ];
  }

  return presenceData;
}

// Clear the presence when paused or disabled
function clearPresence() {
  try {
    if (discordPipe.connected && websocket && websocket.readyState === WebSocket.OPEN) {
      websocket.send(JSON.stringify({
        type: 'clear_activity'
      }));
    }

    currentActivity = null;
  } catch (error) {
    console.error("Error clearing presence:", error);
  }
}

// Listen for messages from content script or popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.type) {
    case "updateActivity":
      updatePresence(message.data);
      sendResponse({ success: true });
      break;
    case "clearActivity":
      clearPresence();
      sendResponse({ success: true });
      break;
    case "getConnectionStatus":
      sendResponse({
        connected: discordPipe.connected,
        attempts: connectionAttempts,
        maxAttempts: MAX_RECONNECT_ATTEMPTS
      });
      break;
    case "forceReconnect":
      console.log("Force reconnect requested");
      connectionAttempts = 0;
      if (websocket) {
        websocket.close();
        websocket = null;
      }
      discordPipe.connected = false;
      connectToDiscordClient();
      sendResponse({ success: true });
      break;
    case "getActivityData":
      sendResponse({ currentActivity: currentActivity });
      break;
  }
  return true; // Keep the message channel open for async response
});

// Initialize on service worker start
init();
