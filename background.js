// Discord Rich Presence for YouTube Music - Background Service Worker

// Discord RPC client configuration
const CLIENT_ID = '123456789012345678'; // Replace with your Discord application client ID
const discordPipe = { connected: false };
let nativePort = null;
let currentActivity = null;
let lastUpdateTime = 0;
let connectionAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_INTERVAL = 10000; // 10 seconds
const UPDATE_THROTTLE = 15000; // 15 seconds (Discord rate limit is 1 update per 15 seconds)

// Initialize the extension
async function init() {
  try {
    // Load user settings
    const userSettings = await loadSettings();
    
    // Attempt to connect to Discord
    connectToDiscordClient();
    
    // Set up periodic connection check
    setInterval(checkDiscordConnection, 30000);
  } catch (error) {
    console.error("Failed to initialize extension:", error);
  }
}

// Connect to the Discord client via native messaging
function connectToDiscordClient() {
  try {
    if (discordPipe.connected) return;
    
    connectionAttempts++;
    console.log(`Attempting to connect to Discord (attempt ${connectionAttempts})`);
    
    nativePort = chrome.runtime.connectNative("com.discord.rich_presence");
    
    nativePort.onMessage.addListener((message) => {
      if (message.event === "connected") {
        console.log("Successfully connected to Discord");
        discordPipe.connected = true;
        connectionAttempts = 0;
        
        // Send initial presence update
        if (currentActivity) {
          updatePresence(currentActivity);
        }
      }
    });
    
    nativePort.onDisconnect.addListener(() => {
      console.log("Disconnected from Discord", chrome.runtime.lastError);
      discordPipe.connected = false;
      nativePort = null;
      
      // Attempt to reconnect if not too many attempts
      if (connectionAttempts < MAX_RECONNECT_ATTEMPTS) {
        setTimeout(connectToDiscordClient, RECONNECT_INTERVAL);
      } else {
        console.error("Failed to connect to Discord after multiple attempts");
        // Notify user of connection issues
        chrome.runtime.sendMessage({ type: "connectionFailed" });
      }
    });
  } catch (error) {
    console.error("Error connecting to Discord:", error);
    discordPipe.connected = false;
  }
}

// Check Discord connection and reconnect if needed
function checkDiscordConnection() {
  if (!discordPipe.connected) {
    connectionAttempts = 0; // Reset counter for scheduled checks
    connectToDiscordClient();
  }
}

// Load user settings from storage
async function loadSettings() {
  return new Promise((resolve) => {
    chrome.storage.sync.get({
      enabled: true,
      showSongTitle: true,
      showArtist: true,
      showAlbum: true,
      showTimestamp: true,
      showAlbumArt: true,
      privateMode: false
    }, (settings) => {
      resolve(settings);
    });
  });
}

// Update Discord Rich Presence with new activity data
async function updatePresence(activity) {
  try {
    // Check if throttling needed (Discord rate limit)
    const now = Date.now();
    if (now - lastUpdateTime < UPDATE_THROTTLE) {
      return;
    }
    
    // Load current user settings
    const settings = await loadSettings();
    
    // Don't update if extension is disabled or in private mode
    if (!settings.enabled) {
      clearPresence();
      return;
    }
    
    if (!discordPipe.connected) {
      connectToDiscordClient();
      return;
    }
    
    // Create presence data object based on user settings
    const presenceData = createPresenceData(activity, settings);
    
    // Send to Discord via native messaging
    nativePort.postMessage({
      cmd: 'SET_ACTIVITY',
      args: {
        pid: 1234, // Arbitrary process ID
        activity: presenceData
      }
    });
    
    lastUpdateTime = now;
    
    // Store current activity for reconnection purposes
    currentActivity = activity;
    
  } catch (error) {
    console.error("Error updating presence:", error);
  }
}

// Create the presence data object based on user settings
function createPresenceData(activity, settings) {
  if (settings.privateMode) {
    // Simplified presence for private mode
    return {
      details: "Listening to YouTube Music",
      state: "Private Mode",
      assets: {
        large_image: "ytmusic_logo",
        large_text: "YouTube Music"
      },
      timestamps: activity.timestamps
    };
  }
  
  const presenceData = {
    assets: {
      large_image: settings.showAlbumArt && activity.albumArt ? activity.albumArt : "ytmusic_logo",
      large_text: settings.showAlbum && activity.album ? activity.album : "YouTube Music"
    }
  };
  
  // Add details (song title)
  if (settings.showSongTitle && activity.title) {
    presenceData.details = activity.title.slice(0, 128);
  } else {
    presenceData.details = "Listening to YouTube Music";
  }
  
  // Add state (artist)
  if (settings.showArtist && activity.artist) {
    presenceData.state = activity.artist.slice(0, 128);
  }
  
  // Add timestamps
  if (settings.showTimestamp && activity.timestamps) {
    presenceData.timestamps = activity.timestamps;
  }
  
  // Add buttons if available
  if (activity.songUrl) {
    presenceData.buttons = [
      {
        label: "Listen on YouTube Music",
        url: activity.songUrl
      }
    ];
  }
  
  return presenceData;
}

// Clear the presence when paused or disabled
function clearPresence() {
  try {
    if (discordPipe.connected) {
      nativePort.postMessage({
        cmd: 'SET_ACTIVITY',
        args: {
          pid: 1234,
          activity: null
        }
      });
    }
    
    currentActivity = null;
  } catch (error) {
    console.error("Error clearing presence:", error);
  }
}

// Listen for messages from content script or popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.type) {
    case "updateActivity":
      updatePresence(message.data);
      break;
    case "clearActivity":
      clearPresence();
      break;
    case "getConnectionStatus":
      sendResponse({ connected: discordPipe.connected });
      break;
    case "forceReconnect":
      connectionAttempts = 0;
      connectToDiscordClient();
      break;
    case "getActivityData":
      sendResponse({ currentActivity: currentActivity });
      break;
  }
  return true; // Keep the message channel open for async response
});

// Initialize on service worker start
init();