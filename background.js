// Discord Rich Presence for YouTube Music - Enhanced Background Service Worker
// Improved version with Spotify-style album art display and time elapsed bar

// Configuration
const CLIENT_ID = '1375799244400623726';
const WS_URL = 'ws://localhost:8765';
const discordPipe = { connected: false };
let websocket = null;
let currentActivity = null;
let lastUpdateTime = 0;
let currentSong = null;
let connectionAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_INTERVAL = 5000; // 5 seconds
const UPDATE_THROTTLE = 15000; // 15 seconds (Discord rate limit is 1 update per 15 seconds)

// Initialize the extension
async function init() {
  try {
    // Load user settings
    const userSettings = await loadSettings();

    // Attempt to connect to Discord
    connectToDiscordClient();

    // Set up periodic connection check
    setInterval(checkDiscordConnection, 30000);
  } catch (error) {
    console.error("Failed to initialize extension:", error);
  }
}

// Connect to the Discord client via WebSocket
function connectToDiscordClient() {
  if (websocket && (websocket.readyState === WebSocket.CONNECTING || websocket.readyState === WebSocket.OPEN)) {
    console.log("WebSocket is already connecting or connected");
    return;
  }

  console.log("Connecting to WebSocket...");
  try {
    websocket = new WebSocket(WS_URL);

    websocket.onopen = () => {
      console.log("WebSocket connected");
      connectionAttempts = 0;
      discordPipe.connected = true;

      // Send initial hello message
      sendMessage({ type: 'hello', clientId: CLIENT_ID });

      // Broadcast connection status
      broadcastConnectionStatus(true);
    };

    websocket.onmessage = (event) => {
      const message = JSON.parse(event.data);

      // Handle different message types
      switch (message.type) {
        case 'connection_status':
          discordPipe.connected = message.connected;
          broadcastConnectionStatus(message.connected);
          break;
        case 'error':
          console.error("Discord RPC error:", message.error);
          break;
        default:
          console.log("Unknown message type:", message);
      }
    };

    websocket.onclose = (event) => {
      console.log("WebSocket closed:", event);
      discordPipe.connected = false;
      broadcastConnectionStatus(false);

      // Attempt to reconnect with backoff
      if (connectionAttempts < MAX_RECONNECT_ATTEMPTS) {
        connectionAttempts++;
        const timeout = RECONNECT_INTERVAL * Math.pow(2, connectionAttempts - 1);
        console.log(`Reconnecting in ${timeout/1000} seconds (attempt ${connectionAttempts})...`);
        setTimeout(connectToDiscordClient, timeout);
      }
    };

    websocket.onerror = (error) => {
      console.error("WebSocket error:", error);
    };
  } catch (error) {
    console.error("Failed to connect to WebSocket:", error);
    discordPipe.connected = false;
    broadcastConnectionStatus(false);
  }
}

// Send a message through the WebSocket
function sendMessage(message) {
  if (!websocket || websocket.readyState !== WebSocket.OPEN) {
    console.error("WebSocket is not connected");
    return false;
  }

  try {
    websocket.send(JSON.stringify(message));
    return true;
  } catch (error) {
    console.error("Failed to send message:", error);
    return false;
  }
}

// Broadcast connection status to all tabs
function broadcastConnectionStatus(connected) {
  chrome.runtime.sendMessage({
    type: 'connection_status',
    connected: connected
  }).catch(() => {
    // Ignore errors from no receivers
  });
}

// Check Discord connection
function checkDiscordConnection() {
  if (websocket && websocket.readyState === WebSocket.OPEN) {
    sendMessage({ type: 'ping' });
  } else if (connectionAttempts < MAX_RECONNECT_ATTEMPTS) {
    connectToDiscordClient();
  }
}

// Load user settings from storage
async function loadSettings() {
  return new Promise((resolve) => {
    chrome.storage.sync.get({
      enablePresence: true,
      privateMode: false,
      autoConnect: true,
      showTitle: true,
      showArtist: true,
      showAlbum: true,
      showArtwork: true,
      showTimestamps: true,
      showListenButton: true
    }, (settings) => {
      resolve(settings);
    });
  });
}

// Create a text-based progress bar (since Discord doesn't support native progress bars)
function createProgressBar(current, total, length = 10) {
  if (!current || !total) return '';

  const progress = Math.min(Math.max(current / total, 0), 1);
  const filledBlocks = Math.round(progress * length);
  const emptyBlocks = length - filledBlocks;

  // Using Unicode blocks for a nicer visualization
  // Alternative characters: â”â”â”â”â”â–‘â–‘â–‘â–‘â–‘ or â–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–’â–’â–’â–’â–’
  return 'â–°'.repeat(filledBlocks) + 'â–±'.repeat(emptyBlocks);
}

// Format time as MM:SS
function formatTime(seconds) {
  if (!seconds) return '0:00';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// Helper to estimate song progress, accounting for time since last update
function calculateEstimatedProgress(songData) {
  if (!songData.isPlaying) {
    return songData.currentTime;
  }

  // If playing, add elapsed time since reported time
  const reportedAt = songData.reportedAt || Date.now();
  const timeElapsedSinceReport = (Date.now() - reportedAt) / 1000;

  return Math.min(songData.currentTime + timeElapsedSinceReport, songData.duration || Infinity);
}

// Create Discord Activity with enhanced visuals
async function createDiscordActivity(songData) {
  if (!songData) return null;

  const settings = await loadSettings();

  // If presence is disabled, don't create activity
  if (!settings.enablePresence) return null;

  const isPrivateMode = settings.privateMode;
  const adjustedTime = calculateEstimatedProgress(songData);

  // Create the Spotify-style activity
  const activity = {
    details: isPrivateMode ? 'Listening to YouTube Music' :
             (settings.showTitle ? songData.title : 'Listening to YouTube Music'),

    state: isPrivateMode ? '' :
           `${settings.showArtist && songData.artist ? songData.artist : ''} ${
             settings.showTimestamps && songData.currentTime && songData.duration ?
               `${createProgressBar(adjustedTime, songData.duration)} ${
                 formatTime(adjustedTime)}/${formatTime(songData.duration)}` :
               ''
           }`.trim(),

    // If song is paused, don't send timestamps to avoid counting when paused
    ...(songData.isPlaying && !isPrivateMode && settings.showTimestamps && songData.duration ? {
      startTimestamp: Date.now() - (adjustedTime * 1000),
      endTimestamp: Date.now() + ((songData.duration - adjustedTime) * 1000)
    } : {}),

    assets: {
      // Use album art as large image for more prominence (Spotify style)
      large_image: isPrivateMode ? 'youtube_music_logo' :
                   (settings.showArtwork && songData.thumbnail ? songData.thumbnail : 'youtube_music_logo'),
      large_text: isPrivateMode ? 'YouTube Music' :
                  (settings.showAlbum && songData.album ? songData.album : 'YouTube Music'),

      // Use YouTube Music logo or play/pause status as small image
      small_image: songData.isPlaying ? 'playing_icon' : 'paused_icon',
      small_text: songData.isPlaying ? 'Playing' : 'Paused',
    },

    // Add buttons if configured and not in private mode
    ...(settings.showListenButton && !isPrivateMode ? {
      buttons: [
        {
          label: 'Listen on YouTube Music',
          url: 'https://music.youtube.com'
        }
      ]
    } : {})
  };

  return activity;
}

// Update Discord Rich Presence activity
async function updateDiscordActivity(songData) {
  if (!songData) return;

  try {
    const now = Date.now();
    const settings = await loadSettings();

    // Don't update if presence is disabled
    if (!settings.enablePresence) {
      if (currentActivity) {
        // Clear activity if it was previously set
        sendMessage({ type: 'clear_activity' });
        currentActivity = null;
      }
      return;
    }

    // Always update on song change or play/pause toggle
    const songChanged = currentActivity && (
      currentActivity.details !== songData.title ||
      currentActivity.isPlaying !== songData.isPlaying
    );

    // Respect Discord's rate limit unless song changed
    if (!songChanged && (now - lastUpdateTime < UPDATE_THROTTLE)) {
      // Too soon, skip this update
      return;
    }

    const activity = await createDiscordActivity(songData);

    if (!activity) {
      // No activity to set (e.g., presence disabled)
      return;
    }

    // Save current activity for comparison
    currentActivity = {
      details: songData.title,
      isPlaying: songData.isPlaying
    };

    // Send activity update
    sendMessage({
      type: 'set_activity',
      activity: activity
    });

    lastUpdateTime = now;
  } catch (error) {
    console.error("Failed to update Discord activity:", error);
  }
}

// Handle messages from content scripts
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  switch (message.type) {
    case "updateActivity":
      updateDiscordActivity(message.data);
      sendResponse({ success: true });
      break;
    case "clearActivity":
      if (websocket && websocket.readyState === WebSocket.OPEN) {
        sendMessage({ type: 'clear_activity' });
        currentActivity = null;
      }
      sendResponse({ success: true });
      break;
    case "getConnectionStatus":
      sendResponse({
        connected: discordPipe.connected,
        attempts: connectionAttempts,
        maxAttempts: MAX_RECONNECT_ATTEMPTS
      });
      break;
    case "forceReconnect":
      console.log("Force reconnect requested");
      connectionAttempts = 0;
      if (websocket) {
        websocket.close();
        websocket = null;
      }
      discordPipe.connected = false;
      connectToDiscordClient();
      sendResponse({ success: true });
      break;
    case "getActivityData":
      sendResponse({ currentActivity: currentActivity });
      break;
  }
  return true; // Keep the message channel open for async response
});

// Initialize the extension
init();