// Discord Rich Presence for YouTube Music - Background Service Worker (WebSocket Version)

// Configuration
const CLIENT_ID = '1375799244400623726';
const WS_URL = 'ws://localhost:8765';
const discordPipe = { connected: false };
let websocket = null;
let currentActivity = null;
let lastUpdateTime = 0;
let connectionAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_INTERVAL = 5000; // 5 seconds
const UPDATE_THROTTLE = 15000; // 15 seconds (Discord rate limit is 1 update per 15 seconds)

// Initialize the extension
async function init() {
  try {
    // Load user settings
    const userSettings = await loadSettings();

    // Attempt to connect to Discord
    connectToDiscordClient();

    // Set up periodic connection check
    setInterval(checkDiscordConnection, 30000);
  } catch (error) {
    console.error("Failed to initialize extension:", error);
  }
}

// Connect to the Discord client via WebSocket
function connectToDiscordClient() {
  try {
    if (discordPipe.connected) return;

    connectionAttempts++;
    console.log(`Attempting to connect to WebSocket server (attempt ${connectionAttempts})`);

    // Close existing connection if any
    if (websocket) {
      websocket.close();
      websocket = null;
    }

    // Create WebSocket connection
    websocket = new WebSocket(WS_URL);

    websocket.onopen = () => {
      console.log("Connected to WebSocket server");
      // Request Discord connection
      websocket.send(JSON.stringify({ type: 'connect_discord' }));
    };

    websocket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        console.log("Received WebSocket message:", message);

        switch (message.event) {
          case 'discord_connected':
            console.log("Successfully connected to Discord via WebSocket");
            discordPipe.connected = true;
            connectionAttempts = 0;

            // Send initial presence update
            if (currentActivity) {
              updatePresence(currentActivity);
            }
            break;

          case 'discord_disconnected':
            console.log("Discord disconnected");
            discordPipe.connected = false;
            break;

          case 'discord_connection_failed':
            console.error("Discord connection failed:", message.error);
            discordPipe.connected = false;
            break;

          case 'activity_set':
            console.log("Activity set successfully");
            break;

          case 'error':
            console.error("WebSocket error:", message.message);
            break;
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    };

    websocket.onclose = () => {
      console.log("WebSocket connection closed");
      discordPipe.connected = false;
      websocket = null;

      // Attempt to reconnect if not too many attempts
      if (connectionAttempts < MAX_RECONNECT_ATTEMPTS) {
        setTimeout(() => connectToDiscordClient(), RECONNECT_INTERVAL);
      } else {
        console.error("Failed to connect to WebSocket server after multiple attempts");
      }
    };

    websocket.onerror = (error) => {
      console.error("WebSocket error:", error);
      discordPipe.connected = false;
    };

  } catch (error) {
    console.error("Error connecting to WebSocket:", error);
    discordPipe.connected = false;

    // Retry after delay if not too many attempts
    if (connectionAttempts < MAX_RECONNECT_ATTEMPTS) {
      setTimeout(() => connectToDiscordClient(), RECONNECT_INTERVAL);
    }
  }
}

// Check Discord connection and reconnect if needed
function checkDiscordConnection() {
  if (!discordPipe.connected) {
    connectionAttempts = 0; // Reset counter for scheduled checks
    connectToDiscordClient();
  }
}

// Load user settings from storage
async function loadSettings() {
  return new Promise((resolve) => {
    chrome.storage.sync.get({
      enabled: true,
      showSongTitle: true,
      showArtist: true,
      showAlbum: true,
      showTimestamp: true,
      showAlbumArt: true,
      privateMode: false
    }, (settings) => {
      resolve(settings);
    });
  });
}

// Update Discord Rich Presence with new activity data
async function updatePresence(activity) {
  try {
    // Check if throttling needed (Discord rate limit)
    const now = Date.now();
    if (now - lastUpdateTime < UPDATE_THROTTLE) {
      return;
    }

    // Load current user settings
    const settings = await loadSettings();

    // Don't update if extension is disabled or in private mode
    if (!settings.enabled) {
      clearPresence();
      return;
    }

    if (!discordPipe.connected) {
      connectToDiscordClient();
      return;
    }

    // Create presence data object based on user settings
    const presenceData = createPresenceData(activity, settings);

    // Send to Discord via WebSocket
    if (websocket && websocket.readyState === WebSocket.OPEN) {
      websocket.send(JSON.stringify({
        type: 'set_activity',
        activity: presenceData
      }));
    }

    lastUpdateTime = now;

    // Store current activity for reconnection purposes
    currentActivity = activity;

  } catch (error) {
    console.error("Error updating presence:", error);
  }
}

// Create the presence data object based on user settings
function createPresenceData(activity, settings) {
  if (settings.privateMode) {
    // Simplified presence for private mode
    return {
      details: "Listening to YouTube Music",
      state: "Private Mode",
      assets: {
        large_image: "ytmusic_logo",
        large_text: "YouTube Music"
      },
      timestamps: activity.timestamps
    };
  }

  // Create enhanced presence with rich information
  const presenceData = {
    assets: {
      large_image: settings.showAlbumArt && activity.albumArt ? activity.albumArt : "ytmusic_logo",
      large_text: createLargeImageText(activity, settings),
      small_image: createSmallImage(activity),
      small_text: createSmallImageText(activity)
    }
  };

  // Enhanced details with more information
  presenceData.details = createDetailsText(activity, settings);

  // Enhanced state with rich information
  presenceData.state = createStateText(activity, settings);

  // Add timestamps with enhanced info
  if (settings.showTimestamp && activity.timestamps) {
    presenceData.timestamps = activity.timestamps;
  }

  // Enhanced buttons with more options
  presenceData.buttons = createButtons(activity, settings);

  return presenceData;
}

// Create enhanced details text
function createDetailsText(activity, settings) {
  if (!settings.showSongTitle || !activity.title) {
    return "Listening to YouTube Music";
  }

  let details = activity.title.slice(0, 100); // Leave room for additional info

  // Add year if available
  if (activity.year && settings.showAlbum) {
    details += ` (${activity.year})`;
  }

  // Add live indicator
  if (activity.isLive) {
    details = "🔴 " + details;
  }

  // Add liked indicator
  if (activity.isLiked) {
    details = "❤️ " + details;
  }

  return details.slice(0, 128);
}

// Create enhanced state text
function createStateText(activity, settings) {
  let stateParts = [];

  // Artist name
  if (settings.showArtist && activity.artist) {
    stateParts.push(activity.artist);
  }

  // Album name
  if (settings.showAlbum && activity.album && activity.album !== activity.artist) {
    stateParts.push(`• ${activity.album}`);
  }

  // Playlist info
  if (activity.playlistName && activity.playlistName !== activity.album) {
    stateParts.push(`• ${activity.playlistName}`);
  }

  // Queue position
  if (activity.queuePosition > 0 && activity.queueTotal > 1) {
    stateParts.push(`• ${activity.queuePosition}/${activity.queueTotal}`);
  }

  // Progress percentage
  if (activity.progressPercentage > 0) {
    const progressBar = createProgressBar(activity.progressPercentage);
    stateParts.push(`• ${progressBar} ${activity.progressPercentage}%`);
  }

  return stateParts.join(' ').slice(0, 128);
}

// Create progress bar visualization
function createProgressBar(percentage) {
  const barLength = 10;
  const filledLength = Math.round((percentage / 100) * barLength);
  const emptyLength = barLength - filledLength;

  return '█'.repeat(filledLength) + '░'.repeat(emptyLength);
}

// Create large image text with enhanced info
function createLargeImageText(activity, settings) {
  if (settings.showAlbum && activity.album) {
    let text = activity.album;

    if (activity.year) {
      text += ` (${activity.year})`;
    }

    if (activity.viewCount) {
      text += ` • ${activity.viewCount} views`;
    }

    return text.slice(0, 128);
  }

  return "YouTube Music";
}

// Create small image based on playback state
function createSmallImage(activity) {
  // Use different icons based on state
  if (activity.isLive) {
    return "live_icon";
  }

  if (activity.shuffleMode) {
    return "shuffle_icon";
  }

  if (activity.repeatMode === 'one') {
    return "repeat_one_icon";
  }

  if (activity.repeatMode === 'all') {
    return "repeat_all_icon";
  }

  if (activity.isLiked) {
    return "liked_icon";
  }

  return "play_icon";
}

// Create small image text
function createSmallImageText(activity) {
  let textParts = [];

  if (activity.isLive) {
    textParts.push("🔴 LIVE");
  }

  if (activity.shuffleMode) {
    textParts.push("🔀 Shuffle");
  }

  if (activity.repeatMode === 'one') {
    textParts.push("🔂 Repeat One");
  } else if (activity.repeatMode === 'all') {
    textParts.push("🔁 Repeat All");
  }

  if (activity.volume < 100) {
    textParts.push(`🔊 ${activity.volume}%`);
  }

  if (activity.nextSong) {
    textParts.push(`Next: ${activity.nextSong.slice(0, 30)}`);
  }

  return textParts.join(' • ').slice(0, 128) || "Playing";
}

// Create enhanced buttons
function createButtons(activity, settings) {
  const buttons = [];

  // Primary button - Listen on YouTube Music
  if (activity.songUrl) {
    buttons.push({
      label: "🎵 Listen on YouTube Music",
      url: activity.songUrl
    });
  }

  // Secondary button - View Artist/Channel
  if (activity.channelUrl && buttons.length < 2) {
    buttons.push({
      label: `👤 View ${activity.channelName || 'Artist'}`,
      url: activity.channelUrl
    });
  }

  // Playlist button
  if (activity.playlistId && buttons.length < 2) {
    const playlistUrl = `https://music.youtube.com/playlist?list=${activity.playlistId}`;
    buttons.push({
      label: `📋 View Playlist`,
      url: playlistUrl
    });
  }

  return buttons;
}

// Clear the presence when paused or disabled
function clearPresence() {
  try {
    if (discordPipe.connected && websocket && websocket.readyState === WebSocket.OPEN) {
      websocket.send(JSON.stringify({
        type: 'clear_activity'
      }));
    }

    currentActivity = null;
  } catch (error) {
    console.error("Error clearing presence:", error);
  }
}

// Listen for messages from content script or popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.type) {
    case "updateActivity":
      updatePresence(message.data);
      sendResponse({ success: true });
      break;
    case "clearActivity":
      clearPresence();
      sendResponse({ success: true });
      break;
    case "getConnectionStatus":
      sendResponse({
        connected: discordPipe.connected,
        attempts: connectionAttempts,
        maxAttempts: MAX_RECONNECT_ATTEMPTS
      });
      break;
    case "forceReconnect":
      console.log("Force reconnect requested");
      connectionAttempts = 0;
      if (websocket) {
        websocket.close();
        websocket = null;
      }
      discordPipe.connected = false;
      connectToDiscordClient();
      sendResponse({ success: true });
      break;
    case "getActivityData":
      sendResponse({ currentActivity: currentActivity });
      break;
  }
  return true; // Keep the message channel open for async response
});

// Initialize on service worker start
init();
