// Simple test to verify the native messaging host works
const DiscordRPC = require('discord-rpc');

const clientId = '1375799244400623726';
const rpc = new DiscordRPC.Client({ transport: 'ipc' });

console.log('Testing Discord RPC connection...');

rpc.on('ready', () => {
  console.log('✅ Successfully connected to Discord!');
  console.log('User:', rpc.user.username);
  
  // Test setting an activity
  rpc.setActivity({
    details: 'Testing Connection',
    state: 'YouTube Music Rich Presence',
    assets: {
      large_image: 'ytmusic_logo',
      large_text: 'YouTube Music'
    },
    timestamps: {
      start: Date.now()
    }
  }).then(() => {
    console.log('✅ Activity set successfully!');
    console.log('Check your Discord profile - you should see the test activity.');
    
    // Clean up after 10 seconds
    setTimeout(() => {
      rpc.clearActivity();
      rpc.destroy();
      console.log('Test completed. Activity cleared.');
      process.exit(0);
    }, 10000);
  }).catch(console.error);
});

rpc.on('error', (error) => {
  console.error('❌ Discord RPC Error:', error);
  process.exit(1);
});

// Try to connect
rpc.login({ clientId }).catch((error) => {
  console.error('❌ Failed to connect to Discord:', error);
  console.log('Make sure Discord is running and try again.');
  process.exit(1);
});

// Timeout after 30 seconds
setTimeout(() => {
  console.error('❌ Connection timeout - Discord may not be running or RPC may be disabled');
  process.exit(1);
}, 30000);
