# Discord Rich Presence Native Messaging Host Setup

This document explains how to set up the native messaging host that allows the browser extension to communicate with Discord.

## Prerequisites

- Discord desktop app installed
- Brave browser with the YouTube Music Rich Presence extension installed
- Node.js installed on your system

## Setup Instructions

### 1. Create a Discord Application

1. Go to the [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application" and name it (e.g., "YouTube Music Rich Presence")
3. Note down the "Client ID" from the application page
4. In the "Rich Presence" section, upload assets:
   - Upload a YouTube Music logo as "ytmusic_logo"
   - (Optional) Upload additional icons for use with the presence

### 2. Native Messaging Host Installation

#### Windows Setup

1. Create a new folder for the native messaging host:
   ```
   mkdir %USERPROFILE%\YTMusicDiscordRP
   ```

2. Create a file named `host.js` with the following content:

```javascript
const fs = require('fs');
const DiscordRPC = require('discord-rpc');

// Replace with your Discord application client ID
const clientId = '123456789012345678';

// Set up RPC
const rpc = new DiscordRPC.Client({ transport: 'ipc' });
let connected = false;

// Handle browser messages
process.stdin.on('readable', () => {
  let input = [];
  let chunk;
  while ((chunk = process.stdin.read()) !== null) {
    input.push(chunk);
  }
  
  if (input.length > 0) {
    const buffer = Buffer.concat(input);
    const msgLen = buffer.readUInt32LE(0);
    const dataBuffer = buffer.slice(4, msgLen + 4);
    
    try {
      const message = JSON.parse(dataBuffer.toString());
      handleMessage(message);
    } catch (error) {
      console.error('Error parsing message:', error);
    }
  }
});

// Handle incoming messages
function handleMessage(message) {
  if (!message || !message.cmd) return;
  
  switch (message.cmd) {
    case 'SET_ACTIVITY':
      if (!connected) {
        connectToDiscord()
          .then(() => setActivity(message.args.activity))
          .catch(console.error);
      } else {
        setActivity(message.args.activity);
      }
      break;
  }
}

// Connect to Discord
function connectToDiscord() {
  if (connected) return Promise.resolve();
  
  return new Promise((resolve, reject) => {
    rpc.on('ready', () => {
      connected = true;
      sendMessage({ event: 'connected' });
      resolve();
    });
    
    rpc.login({ clientId }).catch(err => {
      console.error('Error connecting to Discord:', err);
      reject(err);
    });
  });
}

// Set Discord activity
function setActivity(activity) {
  if (!connected || !activity) return;
  
  rpc.setActivity(activity)
    .catch(console.error);
}

// Send message back to browser
function sendMessage(message) {
  const json = JSON.stringify(message);
  const buffer = Buffer.from(json);
  
  const header = Buffer.alloc(4);
  header.writeUInt32LE(buffer.length, 0);
  
  process.stdout.write(Buffer.concat([header, buffer]));
}

// Handle process exit
process.on('SIGINT', () => {
  rpc.destroy();
  process.exit();
});
```

3. Create a file named `package.json`:

```json
{
  "name": "discord-rich-presence-host",
  "version": "1.0.0",
  "description": "Native messaging host for YouTube Music Discord Rich Presence",
  "main": "host.js",
  "dependencies": {
    "discord-rpc": "^4.0.1"
  }
}
```

4. Install dependencies:
   ```
   cd %USERPROFILE%\YTMusicDiscordRP
   npm install
   ```

5. Create a file named `com.discord.rich_presence.json` with the following content (update paths as needed):

```json
{
  "name": "com.discord.rich_presence",
  "description": "YouTube Music Discord Rich Presence",
  "path": "C:\\Windows\\System32\\cmd.exe",
  "type": "stdio",
  "args": ["/c", "node", "%USERPROFILE%\\YTMusicDiscordRP\\host.js"],
  "allowed_origins": [
    "chrome-extension://EXTENSION_ID/"
  ]
}
```

6. Register the native messaging host:
   - Create a registry entry:
     - Open Registry Editor (regedit.exe)
     - Navigate to `HKEY_CURRENT_USER\Software\Google\Chrome\NativeMessagingHosts\com.discord.rich_presence`
     - Create a new string value with the name `(Default)` and value `%USERPROFILE%\YTMusicDiscordRP\com.discord.rich_presence.json`
   - For Brave, also navigate to `HKEY_CURRENT_USER\Software\BraveSoftware\Brave-Browser\NativeMessagingHosts\com.discord.rich_presence`
     - Create a new string value with the name `(Default)` and the same value as above

#### macOS and Linux Setup

1. Create a new folder for the native messaging host:
```
mkdir -p ~/.config/ytmusic-discord-rp
```

2. Create the host.js file in that folder with the same content as the Windows version.

3. Create a file named `com.discord.rich_presence.json`:

```json
{
  "name": "com.discord.rich_presence",
  "description": "YouTube Music Discord Rich Presence",
  "path": "/usr/bin/node",
  "type": "stdio",
  "args": ["$HOME/.config/ytmusic-discord-rp/host.js"],
  "allowed_origins": [
    "chrome-extension://EXTENSION_ID/"
  ]
}
```

4. For macOS, place the JSON file at:
```
~/Library/Application Support/Google/Chrome/NativeMessagingHosts/com.discord.rich_presence.json
~/Library/Application Support/BraveSoftware/Brave-Browser/NativeMessagingHosts/com.discord.rich_presence.json
```

5. For Linux, place the JSON file at:
```
~/.config/google-chrome/NativeMessagingHosts/com.discord.rich_presence.json
~/.config/BraveSoftware/Brave-Browser/NativeMessagingHosts/com.discord.rich_presence.json
```

## Finding Your Extension ID

1. Install the extension in developer mode
2. Go to brave://extensions
3. Find the extension and note its ID (a long string of letters and numbers)
4. Replace "EXTENSION_ID" in the JSON file with your actual extension ID

## Troubleshooting

If the extension isn't connecting to Discord:

1. Make sure Discord is running
2. Check that the native messaging host is properly registered
3. Ensure the client ID in host.js matches your Discord application
4. Check browser console for error messages
5. Verify that the paths in the JSON file are correct for your system
6. Try restarting both Discord and the browser

## Manual Testing

To test the native messaging host manually:

1. Run the host script directly: `node host.js`
2. In a separate terminal, send a test message:
```javascript
const message = {
  cmd: 'SET_ACTIVITY',
  args: {
    pid: 1234,
    activity: {
      details: 'Test Song',
      state: 'Test Artist',
      assets: {
        large_image: 'ytmusic_logo',
        large_text: 'YouTube Music'
      },
      timestamps: {
        start: Math.floor(Date.now() / 1000)
      }
    }
  }
};

const json = JSON.stringify(message);
const buffer = Buffer.from(json);
const header = Buffer.alloc(4);
header.writeUInt32LE(buffer.length, 0);
process.stdout.write(Buffer.concat([header, buffer]));
```

If successful, you should see a Discord Rich Presence update with the test song information.