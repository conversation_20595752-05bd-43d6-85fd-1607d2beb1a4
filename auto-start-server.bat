@echo off
title YouTube Music Discord RPC - Smart Auto-Starter
color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║          YouTube Music Discord RPC - Smart Auto-Starter     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: Change to the script directory
cd /d "%~dp0"

:: Check if Node.js is installed
echo [1/4] Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Node.js is installed
)

:: Check if dependencies are installed
echo [2/4] Checking dependencies...
if not exist "node_modules\ws" (
    echo 📦 Installing dependencies...
    npm install ws discord-rpc
    if errorlevel 1 (
        echo ❌ ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully
) else (
    echo ✅ Dependencies are already installed
)

:: Check if server is already running
echo [3/4] Checking if server is already running...
netstat -an | find ":8765" >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  Server is already running on port 8765
    echo You can close this window.
    timeout /t 3 /nobreak >nul
    exit /b 0
)

:: Start the WebSocket server
echo [4/4] Starting Discord RPC WebSocket server...
echo.
echo 🚀 Server starting on localhost:8765
echo 🎵 Open YouTube Music to see your Discord Rich Presence!
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║  The server is now running. Keep this window open while      ║
echo ║  using YouTube Music. Close it to stop the Discord RPC.     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

node websocket-host.js

echo.
echo Server stopped. Press any key to exit...
pause >nul
