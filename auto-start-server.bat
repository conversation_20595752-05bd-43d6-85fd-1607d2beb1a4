@echo off
title YouTube Music Discord RPC Auto-Starter
echo Starting YouTube Music Discord RPC Server...
echo.

:: Change to the script directory
cd /d "%~dp0"

:: Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

:: Check if dependencies are installed
if not exist "node_modules\ws" (
    echo Installing dependencies...
    npm install ws discord-rpc
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

:: Start the WebSocket server
echo Starting WebSocket server on port 8765...
echo.
echo The server will run in the background.
echo Close this window to stop the server.
echo.

node websocket-host.js

pause
